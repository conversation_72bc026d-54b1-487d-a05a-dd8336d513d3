
import React, { useState, useEffect } from 'react';
import CostStructureManager from '@/components/cost/CostStructureManager';
import { useIsMobile } from '@/hooks/use-mobile';
import { useLocation } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Package } from 'lucide-react';
import { Link } from 'react-router-dom';

const CostManager: React.FC = () => {
  const isMobile = useIsMobile();
  const location = useLocation();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState<string>("costStructure");
  
  useEffect(() => {
    // Check if we have walkthrough data in the location state
    if (location.state && location.state.walkthroughData) {
      const { rooms, systemCategories } = location.state.walkthroughData;
      
      // If we have rooms data, show a notification
      if (rooms && rooms.length > 0) {
        // Display a notification
        toast({
          title: "Walkthrough Data Imported",
          description: `Imported data for ${rooms.length} rooms`
        });
      }
    }
  }, [location, toast]);
  
  return (
    <div className="container mx-auto py-4 md:py-8 px-3 md:px-4">
      <div className="animate-fade-in">
        <h1 className="text-xl md:text-2xl font-bold mb-2 md:mb-6">Cost Manager</h1>
        <p className="mb-4 md:mb-8 text-sm md:text-base text-muted-foreground">
          Create and manage cost structures for your quotes and projects.
        </p>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
          <TabsList className="mb-4">
            <TabsTrigger value="costStructure">Cost Structure</TabsTrigger>
            <TabsTrigger value="productCatalog">Product Catalog</TabsTrigger>
          </TabsList>
          
          <TabsContent value="costStructure">
            <div className={`${isMobile ? 'px-0' : 'px-4'} pb-6`}>
              <CostStructureManager 
                walkthroughData={location.state?.walkthroughData}
              />
            </div>
          </TabsContent>
          
          <TabsContent value="productCatalog">
            <div className="flex flex-col items-center justify-center py-12 px-4 border rounded-lg">
              <Package className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-xl font-medium mb-2">Manage Your Product Catalog</h3>
              <p className="text-center text-muted-foreground mb-6 max-w-md">
                Add, edit, and organize your products in the catalog to easily add them to cost structures.
              </p>
              <Button asChild>
                <Link to="/products">
                  <Package className="mr-2 h-4 w-4" />
                  Go to Product Catalog
                </Link>
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default CostManager;
