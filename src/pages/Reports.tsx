
import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { FileText, Download, ChevronDown, Search, Filter, FileSpreadsheet } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { getWalkthroughs, getWalkthroughById } from '@/services/walkthroughService';
import { generateProjectPDF } from '@/utils/pdfGenerator';
import { format } from 'date-fns';
import { useIsMobile } from '@/hooks/use-mobile';

const Reports = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const clientId = searchParams.get('clientId');
  const isMobile = useIsMobile();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('all');

  const { data: walkthroughs, isLoading } = useQuery({
    queryKey: ['walkthroughs'],
    queryFn: getWalkthroughs,
  });

  const { data: selectedClient } = useQuery({
    queryKey: ['walkthrough', clientId],
    queryFn: () => clientId ? getWalkthroughById(clientId) : null,
    enabled: !!clientId,
  });

  useEffect(() => {
    if (clientId && selectedClient) {
      setActiveTab('client');
    }
  }, [clientId, selectedClient]);

  const filteredReports = walkthroughs?.filter(report =>
    report.client_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    report.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleGenerateReport = (walkthrough: any) => {
    try {
      generateProjectPDF(walkthrough.form_data);
    } catch (error) {
      console.error("Error generating PDF report:", error);
    }
  };

  return (
    <div className="container mx-auto py-4 md:py-8 px-3 md:px-4">
      <div className="animate-fade-in">
        <div className="flex items-center justify-between mb-4 md:mb-6">
          <div className="flex items-center">
            <FileText className="h-5 w-5 md:h-6 md:w-6 mr-2" />
            <h1 className="text-xl md:text-2xl font-bold">Reports</h1>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-4 md:mb-6">
          <TabsList className={`grid grid-cols-2 ${isMobile ? "w-full" : "w-[400px]"}`}>
            <TabsTrigger value="all">All Reports</TabsTrigger>
            <TabsTrigger value="client" disabled={!selectedClient}>
              {selectedClient ? (isMobile ? "Client" : `${selectedClient.client_name}`) : 'Client Reports'}
            </TabsTrigger>
          </TabsList>
          <TabsContent value="all">
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-lg md:text-xl">All Client Reports</CardTitle>
                <CardDescription>View and generate reports for all client walkthroughs</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex justify-between mb-4">
                  <div className="relative w-full max-w-sm">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                    <Input
                      placeholder="Search reports..."
                      className="pl-10"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                </div>

                {isLoading ? (
                  <div className="flex justify-center py-6 md:py-8">
                    <p className="text-sm md:text-base">Loading reports...</p>
                  </div>
                ) : filteredReports && filteredReports.length > 0 ? (
                  isMobile ? (
                    <div className="space-y-4">
                      {filteredReports.map((report) => (
                        <Card key={report.id} className="overflow-hidden border">
                          <CardContent className="p-3 md:p-4">
                            <div className="flex justify-between items-center mb-2">
                              <h3 className="font-medium text-sm md:text-base truncate max-w-[180px]">{report.client_name}</h3>
                              <span className="text-xs text-muted-foreground whitespace-nowrap">
                                {format(new Date(report.updated_at), 'MMM d, yyyy')}
                              </span>
                            </div>
                            <p className="text-xs md:text-sm mb-3 line-clamp-1 text-muted-foreground">{report.title}</p>
                            <div className="mb-2">
                              {report.last_step >= 5 ? (
                                <span className="text-xs text-green-500 font-medium">Ready</span>
                              ) : (
                                <span className="text-xs text-amber-500 font-medium">Incomplete</span>
                              )}
                            </div>
                            <div className="flex gap-1 justify-end mt-2">
                              <Button
                                variant="outline"
                                size="sm"
                                className="h-8 px-2 text-xs"
                                disabled={report.last_step < 5}
                                onClick={() => handleGenerateReport(report)}
                              >
                                <Download className="h-3.5 w-3.5 mr-1" />
                                PDF
                              </Button>
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm" className="h-8 px-2">
                                    <ChevronDown className="h-3.5 w-3.5" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem onClick={() => navigate(`/walkthrough?id=${report.id}`)}>
                                    Edit Walkthrough
                                  </DropdownMenuItem>
                                  <DropdownMenuItem onClick={() => handleGenerateReport(report)}>
                                    <FileSpreadsheet className="h-4 w-4 mr-2" />
                                    Export CSV
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="overflow-x-auto">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Client Name</TableHead>
                            <TableHead>Project Title</TableHead>
                            <TableHead>Last Updated</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {filteredReports.map((report) => (
                            <TableRow key={report.id}>
                              <TableCell className="font-medium">{report.client_name}</TableCell>
                              <TableCell>{report.title}</TableCell>
                              <TableCell>{format(new Date(report.updated_at), 'MMM d, yyyy')}</TableCell>
                              <TableCell>
                                {report.last_step >= 5 ? (
                                  <span className="text-green-500 font-medium">Ready</span>
                                ) : (
                                  <span className="text-amber-500 font-medium">Incomplete</span>
                                )}
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="flex justify-end gap-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    disabled={report.last_step < 5}
                                    onClick={() => handleGenerateReport(report)}
                                  >
                                    <Download className="h-4 w-4 mr-2" />
                                    PDF
                                  </Button>
                                  <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                      <Button variant="ghost" size="sm">
                                        <ChevronDown className="h-4 w-4" />
                                      </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                      <DropdownMenuItem onClick={() => navigate(`/walkthrough?id=${report.id}`)}>
                                        Edit Walkthrough
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleGenerateReport(report)}>
                                        <FileSpreadsheet className="h-4 w-4 mr-2" />
                                        Export CSV
                                      </DropdownMenuItem>
                                    </DropdownMenuContent>
                                  </DropdownMenu>
                                </div>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  )
                ) : (
                  <div className="text-center py-6 md:py-8">
                    <p className="text-sm md:text-base text-muted-foreground">No reports found</p>
                    {searchQuery ? (
                      <p className="mt-2 text-xs md:text-sm text-muted-foreground">
                        Try adjusting your search criteria
                      </p>
                    ) : (
                      <p className="mt-2 text-xs md:text-sm text-muted-foreground">
                        Complete client walkthroughs to generate reports
                      </p>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="client">
            {selectedClient && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg md:text-xl">{selectedClient.client_name} Reports</CardTitle>
                  <CardDescription>
                    Reports for {selectedClient.title}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4">
                    <div className="bg-muted p-3 md:p-4 rounded-lg">
                      <h3 className="text-base md:text-lg font-medium mb-2">Project Summary Report</h3>
                      <p className="text-xs md:text-sm text-muted-foreground mb-3 md:mb-4">
                        Complete overview of the project including client information, 
                        system specifications, and room configurations.
                      </p>
                      <Button 
                        className="w-full" 
                        size={isMobile ? "sm" : "default"}
                        onClick={() => handleGenerateReport(selectedClient)}
                      >
                        <Download className="h-3.5 w-3.5 md:h-4 md:w-4 mr-1 md:mr-2" />
                        Generate PDF
                      </Button>
                    </div>
                    
                    <div className="bg-muted p-3 md:p-4 rounded-lg">
                      <h3 className="text-base md:text-lg font-medium mb-2">System Requirements</h3>
                      <p className="text-xs md:text-sm text-muted-foreground mb-3 md:mb-4">
                        Detailed specifications of all systems selected for the project.
                      </p>
                      <Button 
                        className="w-full" 
                        variant="outline"
                        size={isMobile ? "sm" : "default"}
                      >
                        <FileSpreadsheet className="h-3.5 w-3.5 md:h-4 md:w-4 mr-1 md:mr-2" />
                        Export CSV
                      </Button>
                    </div>
                  </div>
                  
                  <div className="mt-5 md:mt-6">
                    <h3 className="text-base md:text-lg font-medium mb-3">Report History</h3>
                    {isMobile ? (
                      <Card className="overflow-hidden border">
                        <CardContent className="p-3">
                          <div className="flex justify-between items-center">
                            <div>
                              <h4 className="text-sm font-medium">Project Summary</h4>
                              <p className="text-xs text-muted-foreground">
                                {format(new Date(), 'MMM d, yyyy')}
                              </p>
                            </div>
                            <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        </CardContent>
                      </Card>
                    ) : (
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Report Type</TableHead>
                            <TableHead>Generated On</TableHead>
                            <TableHead className="text-right">Actions</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <TableRow>
                            <TableCell>Project Summary</TableCell>
                            <TableCell>{format(new Date(), 'MMM d, yyyy')}</TableCell>
                            <TableCell className="text-right">
                              <Button size="sm" variant="ghost">
                                <Download className="h-4 w-4" />
                              </Button>
                            </TableCell>
                          </TableRow>
                        </TableBody>
                      </Table>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default Reports;
