
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Package, Grid3X3, Settings, PackagePlus } from 'lucide-react';
import ProductCatalogManager from '@/components/integrations/ProductCatalogManager';
import WeQuotePackageManager from '@/components/integrations/WeQuotePackageManager';
import usePackageManagement from '@/hooks/usePackageManagement';

const ProductsPage: React.FC = () => {
  const { setIsAddDialogOpen } = usePackageManagement();

  return (
    <div className="container mx-auto py-10">
      <div className="mb-8 flex justify-between items-center">
        <h1 className="text-3xl font-bold">Products</h1>
        <Button variant="default" onClick={() => setIsAddDialogOpen(true)}>
          <Package className="mr-2 h-4 w-4" />
          Add Product
        </Button>
      </div>

      <Separator className="mb-6" />

      <Tabs defaultValue="catalog" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="catalog" className="flex items-center">
            <Grid3X3 className="mr-2 h-4 w-4" />
            <span>Product Catalog</span>
          </TabsTrigger>
          <TabsTrigger value="packages" className="flex items-center">
            <PackagePlus className="mr-2 h-4 w-4" />
            <span>Product Packages</span>
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center">
            <Settings className="mr-2 h-4 w-4" />
            <span>Settings</span>
          </TabsTrigger>
        </TabsList>
        <TabsContent value="catalog" className="outline-none">
          <ProductCatalogManager hideHeader={true} />
        </TabsContent>
        <TabsContent value="packages" className="outline-none">
          <WeQuotePackageManager hideHeader={true} />
        </TabsContent>
        <TabsContent value="settings" className="outline-none">
          <div>
            <h2 className="text-2xl font-bold mb-4">Settings</h2>
            <p>Here you can manage settings related to products.</p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ProductsPage;
