
import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { getWalkthroughById } from '@/services/walkthroughService';
import ClientWalkthroughComponent from '@/components/ClientWalkthrough';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { AlertCircle, ArrowLeft } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { 
  Breadcrumb, 
  BreadcrumbList, 
  BreadcrumbItem, 
  BreadcrumbLink, 
  BreadcrumbSeparator,
  BreadcrumbPage
} from "@/components/ui/breadcrumb";

const ClientWalkthrough = () => {
  const { id } = useParams<{ id: string }>();
  const [isLoading, setIsLoading] = useState(true);
  const [walkthroughData, setWalkthroughData] = useState<any>(null);
  const navigate = useNavigate();
  const { toast } = useToast();

  // Handle navigation back to clients page
  const handleBackToClients = () => {
    navigate('/clients');
  };

  useEffect(() => {
    if (!id) {
      navigate('/clients');
      return;
    }

    const fetchWalkthrough = async () => {
      try {
        setIsLoading(true);
        const data = await getWalkthroughById(id);
        if (!data) {
          throw new Error("Walkthrough not found");
        }
        setWalkthroughData(data);
      } catch (error) {
        console.error("Error fetching walkthrough:", error);
        toast({
          title: "Error",
          description: "Could not load the walkthrough. It may have been deleted.",
          variant: "destructive",
        });
        navigate('/clients');
      } finally {
        setIsLoading(false);
      }
    };

    fetchWalkthrough();
  }, [id, navigate, toast]);

  const handleWalkthroughFinish = () => {
    console.log("ClientWalkthrough.tsx: handleWalkthroughFinish called");
    toast({
      title: "Walkthrough Completed",
      description: "Your client walkthrough has been successfully completed."
    });
    
    // Navigate back to clients page after completion
    setTimeout(() => {
      navigate('/clients');
    }, 500);
  };

  if (isLoading) {
    return (
      <div className="container mx-auto py-4 md:py-8 px-3 md:px-4 max-w-4xl">
        <Breadcrumb className="mb-4">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/clients">Clients List</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>Client Walkthrough</BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        <Button 
          variant="ghost" 
          className="mb-4 text-xs md:text-sm flex items-center gap-1 -ml-2 touch-manipulation" 
          onClick={handleBackToClients}
        >
          <ArrowLeft className="h-3 w-3 md:h-4 md:w-4" />
          Back to Clients List
        </Button>
        
        <Alert className="mb-4 md:mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Loading walkthrough data...
          </AlertDescription>
        </Alert>
        
        <div className="bg-white rounded-lg shadow-sm p-4 md:p-6">
          <Skeleton className="w-full h-8 mb-4" />
          <Skeleton className="w-full h-[500px] rounded-md" />
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto animate-fade-in max-w-4xl">
      <div className="px-3 md:px-4 py-4 md:py-6">
        <Breadcrumb className="mb-4">
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/">Home</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink href="/clients">Clients List</BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbPage>
                {walkthroughData?.client_name ? `Edit: ${walkthroughData.client_name}` : 'Client Walkthrough'}
              </BreadcrumbPage>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
        
        <Button 
          variant="ghost" 
          className="mb-4 text-xs md:text-sm flex items-center gap-1 -ml-2 touch-manipulation" 
          onClick={handleBackToClients}
        >
          <ArrowLeft className="h-3 w-3 md:h-4 md:w-4" />
          Back to Clients List
        </Button>
      </div>
        
      {walkthroughData ? (
        <ClientWalkthroughComponent 
          initialWalkthroughId={id}
          initialWalkthroughData={walkthroughData}
          onWalkthroughFinish={handleWalkthroughFinish}
        />
      ) : null}
    </div>
  );
};

export default ClientWalkthrough;
