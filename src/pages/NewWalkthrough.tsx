import React, { useState, useCallback, useEffect, useRef } from 'react';
import { PlusCircle, Building, Home, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useToast } from '@/hooks/use-toast';
import ClientWalkthrough from '@/components/ClientWalkthrough';
import { ClientInfo } from '@/types/formTypes';
import { useNavigate } from 'react-router-dom';

const WalkthroughTypeCard = ({ 
  title, 
  description, 
  icon: Icon, 
  selected, 
  onClick 
}: { 
  title: string;
  description: string;
  icon: React.ElementType;
  selected: boolean;
  onClick: () => void;
}) => (
  <Card 
    className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
      selected 
        ? 'border-primary bg-gradient-to-r from-primary/5 to-secondary/5' 
        : 'hover:border-primary/30 hover:bg-muted/5'
    }`}
    onClick={onClick}
  >
    <CardHeader className="pb-2">
      <div className="flex items-center justify-between">
        <CardTitle className="text-lg font-medium">{title}</CardTitle>
        <div className={`p-2 rounded-full ${selected ? 'bg-primary/10 text-primary' : 'text-muted-foreground'}`}>
          <Icon className="h-6 w-6" />
        </div>
      </div>
    </CardHeader>
    <CardContent>
      <CardDescription>{description}</CardDescription>
    </CardContent>
    {selected && (
      <CardFooter className="pt-0 pb-3 flex justify-start">
        <div className="flex items-center gap-2 text-primary text-sm font-medium">
          <CheckCircle className="h-4 w-4" />
          <span>Selected</span>
        </div>
      </CardFooter>
    )}
  </Card>
);

const NewWalkthrough = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [walkthroughType, setWalkthroughType] = useState<string | null>(null);
  const [firstName, setFirstName] = useState('');
  const [lastName, setLastName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [address, setAddress] = useState('');
  const [city, setCity] = useState('');
  const [state, setState] = useState('');
  const [zipCode, setZipCode] = useState('');
  const [projectType, setProjectType] = useState('');
  const [homeType, setHomeType] = useState('');
  const [architectInfo, setArchitectInfo] = useState('');
  
  const [startedWalkthrough, setStartedWalkthrough] = useState(false);
  const [clientInfo, setClientInfo] = useState<ClientInfo | null>(null);
  const finishHandled = useRef(false);

  const handleStart = (e: React.FormEvent) => {
    e.preventDefault(); // Prevent default form submission which causes page refresh
    
    // Validate form
    if (!walkthroughType || !firstName || !lastName || !email) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    // Create client info with first and last name fields
    const newClientInfo: ClientInfo = {
      firstName: firstName,
      lastName: lastName,
      fullName: `${firstName} ${lastName}`.trim(),
      email: email,
      phone: phone || "",
      address: address || "",
      city: city || "",
      state: state || "",
      zipCode: zipCode || "",
      architectInfo: architectInfo || "",
      homeType: homeType || "",
      projectType: projectType || "",
    };

    setClientInfo(newClientInfo);
    setStartedWalkthrough(true);
    console.log("NewWalkthrough: walkthrough started, clientInfo set", newClientInfo);
  };

  const handleWalkthroughFinish = useCallback(() => {
    // Prevent multiple finish handling
    if (finishHandled.current) return;
    finishHandled.current = true;
    
    console.log("NewWalkthrough: handleWalkthroughFinish called");
    
    // Redirect to appropriate page after walkthrough completion
    toast({
      title: "Walkthrough Completed",
      description: `${firstName} ${lastName}'s walkthrough has been completed successfully.`
    });
    
    // Use React Router navigation instead of setTimeout
    navigate('/clients', { replace: true });
  }, [firstName, lastName, navigate, toast]);

  // Debug to ensure we're set up
  useEffect(() => {
    console.log("NewWalkthrough component mounted/updated, startedWalkthrough:", startedWalkthrough);
    
    // Clean up handler to avoid memory leaks
    return () => {
      finishHandled.current = false;
    };
  }, [startedWalkthrough]);

  if (startedWalkthrough && clientInfo) {
    console.log("NewWalkthrough: Rendering ClientWalkthrough with onWalkthroughFinish");
    return (
      <ClientWalkthrough 
        initialClientInfo={clientInfo} 
        onWalkthroughFinish={handleWalkthroughFinish}
        startAtQualification={true}
      />
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center mb-6">
        <PlusCircle className="h-6 w-6 mr-2 text-primary" />
        <h1 className="text-2xl font-bold">New Client Walkthrough</h1>
      </div>

      <div className="max-w-4xl mx-auto">
        <form 
          className="bg-card rounded-lg shadow-md p-6 mb-6"
          onSubmit={handleStart}
        >
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-primary text-primary-foreground text-sm font-medium">1</span>
            Select Walkthrough Type
          </h2>
          <div className="grid md:grid-cols-2 gap-4 mb-8">
            <WalkthroughTypeCard
              title="Residential Project"
              description="For single-family homes, apartments, and other residential spaces."
              icon={Home}
              selected={walkthroughType === 'residential'}
              onClick={() => setWalkthroughType('residential')}
            />
            <WalkthroughTypeCard
              title="Commercial Project"
              description="For office buildings, retail spaces, and other commercial properties."
              icon={Building}
              selected={walkthroughType === 'commercial'}
              onClick={() => setWalkthroughType('commercial')}
            />
          </div>

          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <span className="inline-flex items-center justify-center w-6 h-6 rounded-full bg-primary text-primary-foreground text-sm font-medium">2</span>
            Client Information
          </h2>
          <div className="space-y-4 mb-8">
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name <span className="text-destructive">*</span></Label>
                <Input
                  id="firstName"
                  placeholder="Enter first name"
                  value={firstName}
                  onChange={(e) => setFirstName(e.target.value)}
                  className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name <span className="text-destructive">*</span></Label>
                <Input
                  id="lastName"
                  placeholder="Enter last name"
                  value={lastName}
                  onChange={(e) => setLastName(e.target.value)}
                  className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
                />
              </div>
            </div>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="email">Email <span className="text-destructive">*</span></Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number</Label>
                <Input
                  id="phone"
                  placeholder="(*************"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
                />
              </div>
            </div>
            
            {/* Address fields */}
            <div className="space-y-2">
              <Label htmlFor="address">Street Address</Label>
              <Input
                id="address"
                placeholder="123 Main St"
                value={address}
                onChange={(e) => setAddress(e.target.value)}
                className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
              />
            </div>
            <div className="grid md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  placeholder="City"
                  value={city}
                  onChange={(e) => setCity(e.target.value)}
                  className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  placeholder="State"
                  value={state}
                  onChange={(e) => setState(e.target.value)}
                  className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="zipCode">ZIP Code</Label>
                <Input
                  id="zipCode"
                  placeholder="ZIP Code"
                  value={zipCode}
                  onChange={(e) => setZipCode(e.target.value)}
                  className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
                />
              </div>
            </div>
            
            <div className="grid md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="projectType">Project Type</Label>
                <Select value={projectType} onValueChange={setProjectType}>
                  <SelectTrigger id="projectType" className="transition-all duration-200 hover:border-primary/50">
                    <SelectValue placeholder="Select project type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="New Build">New Build</SelectItem>
                    <SelectItem value="Renovation">Renovation</SelectItem>
                    <SelectItem value="Upgrade">Upgrade</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="homeType">Home Type</Label>
                <Select value={homeType} onValueChange={setHomeType}>
                  <SelectTrigger id="homeType" className="transition-all duration-200 hover:border-primary/50">
                    <SelectValue placeholder="Select home type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Primary">Primary</SelectItem>
                    <SelectItem value="Vacation">Vacation</SelectItem>
                    <SelectItem value="Rental">Rental</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="architectInfo">Architect / Designer / Builder</Label>
              <Input
                id="architectInfo"
                placeholder="Optional - Enter name or company"
                value={architectInfo}
                onChange={(e) => setArchitectInfo(e.target.value)}
                className="transition-all duration-200 focus:border-primary focus:ring-primary/20"
              />
            </div>
          </div>

          <div className="flex justify-end gap-2 mt-6">
            <Button 
              type="submit" 
              variant="gradient" 
              size="lg" 
              className="relative overflow-hidden font-medium transition-all duration-300 hover:shadow-lg hover:translate-y-[-2px]"
            >
              <span className="relative z-10">Start Walkthrough</span>
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default NewWalkthrough;
