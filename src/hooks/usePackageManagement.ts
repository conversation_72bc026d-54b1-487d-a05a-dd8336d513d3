
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface ProductItem {
  id: string;
  name: string;
  description: string;
  model: string;
  manufacturer: string;
  systemCategory: string;
  unitCost: number;
  quantity: number;
  unit: string;
  subcategories: string[];
  sku: string;
  imageUrl: string;
  msrp: number;
  tradePrice?: number;
  documentUrl?: string;
  documentName?: string;
  isDefault: boolean;
}

export interface CategoryItem {
  id: string;
  name: string;
  system_category: string;
}

// Export systemCategories directly from the file
export const systemCategories = {
  lighting: 'Lighting',
  shades: 'Shades',
  audio: 'Audio',
  video: 'Video',
  network: 'Network',
  security: 'Security',
  controlSystems: 'Control Systems',
};

const usePackageManagement = (options: { isStandalone?: boolean } = {}) => {
  const [items, setItems] = useState<ProductItem[]>([]);
  const [categories, setCategories] = useState<CategoryItem[]>([]);
  const [currentItem, setCurrentItem] = useState<ProductItem | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isImportDialogOpen, setIsImportDialogOpen] = useState(false);
  const [isImportCSVDialogOpen, setIsImportCSVDialogOpen] = useState(false);
  const [templateUrl, setTemplateUrl] = useState('');
  const [systemCategoriesState, setSystemCategoriesState] = useState<Record<string, string>>({});
  const { toast } = useToast();

  useEffect(() => {
    fetchItems();
    loadSystemCategories();
    fetchCategories();
  }, []);

  const fetchItems = async () => {
    try {
      setIsLoading(true);
      const { data, error } = await supabase
        .from('product_catalog_items')
        .select('*')
        .order('name');

      if (error) {
        console.error('Error fetching items:', error);
        return;
      }

      if (data) {
        // Map database items to our format
        const mappedItems = data.map((item: any) => ({
          id: item.id,
          name: item.name,
          description: item.description || '',
          model: item.model || '',
          manufacturer: item.manufacturer || '',
          systemCategory: item.system_category,
          unitCost: item.unit_cost || 0,
          quantity: item.quantity || 1,
          unit: item.unit || 'piece',
          subcategories: item.subcategories || [],
          sku: item.sku_number || '',
          imageUrl: item.image_url || '',
          msrp: item.msrp || 0,
          tradePrice: item.trade_price || 0,
          documentUrl: item.document_url || '',
          documentName: item.document_name || '',
          isDefault: item.is_default || false,
        }));
        setItems(mappedItems);
      }
    } catch (error) {
      console.error('Error fetching items:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('product_categories')
        .select('*')
        .order('name');

      if (error) {
        console.error('Error fetching categories:', error);
        return;
      }

      setCategories(data || []);
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  // We need to modify the loadSystemCategories function to use our exported systemCategories
  const loadSystemCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('product_categories')
        .select('system_category, name')
        .order('name');

      if (!error && data) {
        // We'll still load dynamic categories from the database, but use our defaults as fallback
        const loadedCategories: Record<string, string> = {...systemCategories};
        data.forEach(item => {
          loadedCategories[item.system_category] = item.name;
        });
        // No need to set state for systemCategories anymore as it's a constant export
      }
    } catch (error) {
      console.error('Error loading system categories:', error);
    }
  };

  const handleAddItem = () => {
    setCurrentItem({
      id: crypto.randomUUID(),
      name: '',
      description: '',
      model: '',
      manufacturer: '',
      systemCategory: 'lighting',
      unitCost: 0,
      quantity: 1,
      unit: 'piece',
      subcategories: [],
      sku: '',
      imageUrl: '',
      msrp: 0,
      tradePrice: 0,
      documentUrl: '',
      documentName: '',
      isDefault: true,
    });
    setIsAddDialogOpen(true);
  };

  const handleEditItem = (item: ProductItem) => {
    setCurrentItem(item);
    setIsEditDialogOpen(true);
  };

  const handleDeleteItem = async (item: ProductItem) => {
    try {
      const { error } = await supabase
        .from('product_catalog_items')
        .delete()
        .eq('id', item.id);

      if (error) {
        console.error('Error deleting item:', error);
        return;
      }

      setItems(prev => prev.filter(i => i.id !== item.id));
      toast({
        title: "Product Deleted",
        description: `${item.name} has been removed from the catalog.`,
      })
    } catch (error) {
      console.error('Error deleting item:', error);
    }
  };

  const handleUpdateItem = (field: string, value: any) => {
    setCurrentItem(prev => {
      if (prev) {
        return { ...prev, [field]: value };
      }
      return prev;
    });
  };

  const updateItemSubcategories = async (subcategories: string[]) => {
    if (currentItem) {
      try {
        const { data, error } = await supabase
          .from('product_catalog_items')
          .update({ subcategories: subcategories })
          .eq('id', currentItem.id)
          .select();

        if (error) {
          console.error('Error updating subcategories:', error);
          return;
        }

        setCurrentItem(prev => {
          if (prev) {
            return { ...prev, subcategories: subcategories };
          }
          return prev;
        });

        setItems(prev => {
          return prev.map(item => {
            if (item.id === currentItem.id) {
              return { ...item, subcategories: subcategories };
            }
            return item;
          });
        });
      } catch (error) {
        console.error('Error updating subcategories:', error);
      }
    }
  };

  const handleSaveNewItem = async (newItem: ProductItem) => {
    try {
      const { data, error } = await supabase
        .from('product_catalog_items')
        .insert([
          {
            id: newItem.id,
            name: newItem.name,
            description: newItem.description,
            model: newItem.model,
            manufacturer: newItem.manufacturer,
            system_category: newItem.systemCategory,
            unit_cost: newItem.unitCost,
            quantity: newItem.quantity,
            unit: newItem.unit,
            subcategories: newItem.subcategories,
            sku_number: newItem.sku,
            image_url: newItem.imageUrl,
            msrp: newItem.msrp,
            trade_price: newItem.tradePrice,
            document_url: newItem.documentUrl,
            document_name: newItem.documentName,
            is_default: newItem.isDefault,
          }
        ])
        .select();

      if (error) {
        console.error('Error saving new item:', error);
        return;
      }

      setItems(prev => [...prev, newItem]);
      setIsAddDialogOpen(false);
      toast({
        title: "Product Created",
        description: `${newItem.name} has been added to the catalog.`,
      })
    } catch (error) {
      console.error('Error saving new item:', error);
    }
  };

  const handleSaveExistingItem = async (updatedItem?: ProductItem) => {
    if (!currentItem && !updatedItem) {
      console.error('No current item to update.');
      return;
    }

    const itemToUpdate = updatedItem || currentItem;

    try {
      const { data, error } = await supabase
        .from('product_catalog_items')
        .update({
          name: itemToUpdate.name,
          description: itemToUpdate.description,
          model: itemToUpdate.model,
          manufacturer: itemToUpdate.manufacturer,
          system_category: itemToUpdate.systemCategory,
          unit_cost: itemToUpdate.unitCost,
          quantity: itemToUpdate.quantity,
          unit: itemToUpdate.unit,
          subcategories: itemToUpdate.subcategories,
          sku_number: itemToUpdate.sku,
          image_url: itemToUpdate.imageUrl,
          msrp: itemToUpdate.msrp,
          trade_price: itemToUpdate.tradePrice,
          document_url: itemToUpdate.documentUrl,
          document_name: itemToUpdate.documentName,
          is_default: itemToUpdate.isDefault,
        })
        .eq('id', itemToUpdate.id)
        .select();

      if (error) {
        console.error('Error updating item:', error);
        return;
      }

      setItems(prev =>
        prev.map(item => (item.id === itemToUpdate.id ? itemToUpdate : item))
      );
      setCurrentItem(itemToUpdate);
      setIsEditDialogOpen(false);
      toast({
        title: "Product Updated",
        description: `${itemToUpdate.name} has been updated in the catalog.`,
      })
    } catch (error) {
      console.error('Error updating item:', error);
    }
  };

  const handleImportTemplate = async () => {
    try {
      const response = await fetch(templateUrl);
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      const data = await response.json();

      // Map the data to the ProductItem interface and insert into Supabase
      const itemsToInsert = data.map((item: any) => ({
        id: crypto.randomUUID(),
        name: item.name || 'Unknown',
        description: item.description || '',
        model: item.model || '',
        manufacturer: item.manufacturer || '',
        system_category: item.system_category || 'general',
        unit_cost: item.unit_cost || 0,
        quantity: item.quantity || 1,
        unit: item.unit || 'piece',
        subcategories: item.subcategories || [],
        sku_number: item.sku || '',
        image_url: item.imageUrl || '',
        msrp: item.msrp || 0,
        trade_price: item.tradePrice || 0,
        document_url: item.documentUrl || '',
        document_name: item.documentName || '',
        is_default: true,
      }));

      const { data: insertData, error: insertError } = await supabase
        .from('product_catalog_items')
        .insert(itemsToInsert)
        .select();

      if (insertError) {
        throw new Error(`Supabase insert error: ${insertError.message}`);
      }

      // Update local state with the new items
      setItems(prevItems => {
        const mappedItems = insertData.map((item: any) => ({
          id: item.id,
          name: item.name,
          description: item.description || '',
          model: item.model || '',
          manufacturer: item.manufacturer || '',
          systemCategory: item.system_category,
          unitCost: item.unit_cost || 0,
          quantity: item.quantity || 1,
          unit: item.unit || 'piece',
          subcategories: item.subcategories || [],
          sku: item.sku_number || '',
          imageUrl: item.image_url || '',
          msrp: item.msrp || 0,
          tradePrice: item.trade_price || 0,
          documentUrl: item.document_url || '',
          documentName: item.document_name || '',
          isDefault: item.is_default || false,
        }));
        return [...prevItems, ...mappedItems];
      });

      setIsImportDialogOpen(false);
      toast({
        title: "Template Imported",
        description: `Successfully imported ${itemsToInsert.length} products from the template.`,
      })
    } catch (error: any) {
      console.error('Error importing template:', error);
      toast({
        title: "Import Failed",
        description: `Failed to import template: ${error.message}`,
        variant: "destructive"
      })
    }
  };

  const handleImportCSV = async (csvContent: string) => {
    try {
      // Parse CSV
      const lines = csvContent.trim().split('\n');
      const headers = lines[0].split(',');

      // Process each row from CSV
      const newItems: any[] = [];
      for (let i = 1; i < lines.length; i++) {
        const values = lines[i].split(',');
        if (values.length !== headers.length) {
          console.warn(`Skipping line ${i+1}: column count mismatch`);
          continue;
        }

        // Create object with default field mapping
        let item: Record<string, any> = {
          id: crypto.randomUUID(),
          unit: 'piece',
          unit_cost: 0,
          quantity: 1,
          discontinued: false,
          is_default: false,
        };

        // Map values to fields based on headers
        headers.forEach((header, index) => {
          const headerLower = header.toLowerCase();
          if (headerLower === 'sku') {
            item.sku_number = values[index];
          } else if (headerLower === 'description') {
            item.name = values[index];
          } else if (headerLower === 'model') {
            item.model = values[index];
          } else if (headerLower === 'category') {
            item.system_category = values[index];
          } else if (headerLower === 'manufacturer') {
            item.manufacturer = values[index];
          } else if (headerLower === 'unit_cost') {
            item.unit_cost = parseFloat(values[index]) || 0;
          } else if (headerLower === 'quantity') {
            item.quantity = parseInt(values[index]) || 1;
          } else if (headerLower === 'image_url') {
            item.image_url = values[index];
          } else if (headerLower === 'msrp') {
            item.msrp = parseFloat(values[index]) || 0;
          }
        });

        // Validate required fields
        if (item.name && item.system_category) {
          newItems.push(item);
        } else {
          console.warn('Skipping item due to missing required fields:', item);
        }
      }

      // Insert items into database
      if (newItems.length > 0) {
        const { data, error } = await supabase
          .from('product_catalog_items')
          .insert(newItems)
          .select();

        if (error) {
          console.error('Error inserting items:', error);
          return;
        }

        // Add the new items to our state
        if (data) {
          // Map database items to our format
          const mappedItems = data.map((item: any) => ({
            id: item.id,
            name: item.name,
            description: item.description || '',
            model: item.model || '',
            manufacturer: item.manufacturer || '',
            systemCategory: item.system_category,
            unitCost: item.unit_cost || 0,
            quantity: item.quantity || 1,
            unit: item.unit || 'piece',
            subcategories: item.subcategories || [],
            sku: item.sku_number || '',
            imageUrl: item.image_url || '',
            msrp: item.msrp || 0,
            isDefault: item.is_default || false,
          }));

          setItems(prev => [...prev, ...mappedItems]);
        }

        return newItems.length;
      }

      return 0;
    } catch (error) {
      console.error('Error importing CSV:', error);
      return 0;
    }
  };

  const getItemsByCategory = (category: string, subcategory?: string) => {
    if (subcategory) {
      return items.filter(item => item.systemCategory === category && item.subcategories.includes(subcategory));
    }
    return items.filter(item => item.systemCategory === category);
  };

  const getAllItems = () => {
    return items;
  };

  const getItemsBySubcategory = (subcategory: string) => {
    return items.filter(item => item.subcategories.includes(subcategory));
  };

  // Update CSV import handler to include field mapping
  const handleImportCSVContent = async (csvContent: string, fieldMapping?: Record<string, string>) => {
    try {
      // Parse CSV
      const lines = csvContent.trim().split('\n');
      const headers = parseCSVLine(lines[0]);
      
      // Function to parse CSV lines properly, handling quoted values
      function parseCSVLine(line: string): string[] {
        const result: string[] = [];
        let inQuote = false;
        let currentValue = '';
        
        for (let i = 0; i < line.length; i++) {
          const char = line[i];
          
          if (char === '"' && (i === 0 || line[i-1] !== '\\')) {
            inQuote = !inQuote;
          } else if (char === ',' && !inQuote) {
            result.push(currentValue.trim());
            currentValue = '';
          } else {
            currentValue += char;
          }
        }
        
        if (currentValue) {
          result.push(currentValue.trim());
        }
        
        return result;
      }
      
      // Process each row from CSV
      const newItems: any[] = [];
      for (let i = 1; i < lines.length; i++) {
        if (!lines[i].trim()) continue;
        
        const values = parseCSVLine(lines[i]);
        if (values.length !== headers.length) {
          console.error(`Skipping line ${i+1}: column count mismatch`);
          continue;
        }
        
        // Create object with default field mapping
        let item: Record<string, any> = {
          id: crypto.randomUUID(),
          unit: 'piece',
          unit_cost: 0,
          quantity: 1,
          discontinued: false,
          is_default: false,
        };
        
        // Apply custom field mappings if provided
        if (fieldMapping) {
          headers.forEach((header, index) => {
            const dbField = fieldMapping[header];
            if (dbField && values[index]) {
              // Process numeric fields
              if (['unit_cost', 'trade_price', 'msrp'].includes(dbField)) {
                item[dbField] = parseFloat(values[index]);
              } 
              // Process boolean fields
              else if (dbField === 'discontinued') {
                item[dbField] = values[index].toLowerCase() === 'true';
              }
              // Process subcategories as array
              else if (dbField === 'subcategories') {
                item[dbField] = values[index].split(';').map(s => s.trim());
              }
              else {
                item[dbField] = values[index];
              }
            }
          });
          
          // Map special fields using traditional fixed positions
          if (!fieldMapping['name']) {
            const nameIndex = headers.findIndex(h => 
              h.toLowerCase().includes('name') || 
              h.toLowerCase().includes('description')
            );
            if (nameIndex !== -1) item.name = values[nameIndex];
          }
          
          if (!fieldMapping['system_category']) {
            const categoryIndex = headers.findIndex(h => 
              h.toLowerCase().includes('category')
            );
            if (categoryIndex !== -1) item.system_category = values[categoryIndex];
          }
        } 
        // Traditional fixed mapping (compatibility with old code)
        else {
          // Look for standard expected columns
          headers.forEach((header, index) => {
            const headerLower = header.toLowerCase();
            
            if (headerLower === 'sku' || headerLower.includes('sku number')) {
              item.sku_number = values[index];
            }
            else if (headerLower === 'description' || headerLower.includes('product name') || headerLower.includes('name')) {
              item.name = values[index];
            }
            else if (headerLower.includes('long description')) {
              item.description = values[index];
            }
            else if (headerLower === 'model') {
              item.model = values[index];
            }
            else if (headerLower.includes('category group')) {
              // Skip category group for now
            }
            else if (headerLower === 'category' || headerLower.includes('system')) {
              item.system_category = values[index];
            }
            else if (headerLower === 'manufacturer') {
              item.manufacturer = values[index];
            }
            else if (headerLower.includes('manufacturer sku')) {
              item.manufacturer_sku = values[index];
            }
            else if (headerLower.includes('image url')) {
              item.image_url = values[index];
            }
            else if (headerLower.includes('document name')) {
              item.document_name = values[index];
            }
            else if (headerLower.includes('document url')) {
              item.document_url = values[index];
            }
            else if (headerLower.includes('unit of measure') || headerLower === 'unit') {
              item.unit = values[index] || 'piece';
            }
            else if (headerLower.includes('buy cost') || headerLower.includes('cost') || headerLower === 'price') {
              item.unit_cost = parseFloat(values[index]) || 0;
            }
            else if (headerLower.includes('trade price')) {
              item.trade_price = parseFloat(values[index]) || undefined;
            }
            else if (headerLower.includes('msrp')) {
              item.msrp = parseFloat(values[index]) || undefined;
            }
            else if (headerLower === 'ean') {
              item.ean = values[index];
            }
            else if (headerLower === 'discontinued') {
              item.discontinued = values[index].toLowerCase() === 'true';
            }
          });
        }
        
        // Validate required fields
        if (item.name && item.system_category) {
          newItems.push(item);
        } else {
          console.log('Skipping item due to missing required fields:', item);
        }
      }
      
      // Insert items into database
      if (newItems.length > 0) {
        const { data, error } = await supabase
          .from('product_catalog_items')
          .insert(newItems)
          .select();
          
        if (error) {
          console.error('Error inserting items:', error);
          throw new Error(`Failed to insert items: ${error.message}`);
        }
        
        // Add the new items to our state
        if (data) {
          // Map database items to our format
          const mappedItems = data.map((item: any) => ({
            id: item.id,
            name: item.name,
            description: item.description || '',
            model: item.model || '',
            manufacturer: item.manufacturer || '',
            systemCategory: item.system_category,
            unitCost: item.unit_cost || 0,
            quantity: item.quantity || 1,
            unit: item.unit || 'piece',
            subcategories: item.subcategories || [],
            sku: item.sku_number || '',
            imageUrl: item.image_url || '',
            msrp: item.msrp || 0,
            isDefault: item.is_default || false,
          }));
          
          setItems(prev => [...prev, ...mappedItems]);
        }
        
        return newItems.length;
      }
      
      return 0;
    } catch (error) {
      console.error('Error importing CSV:', error);
      throw error;
    }
  };

  return {
    items,
    // For backward compatibility, expose items as packages too
    packages: items,
    categories,
    currentItem,
    isLoading,
    isAddDialogOpen,
    isEditDialogOpen,
    isImportDialogOpen,
    isImportCSVDialogOpen,
    templateUrl,
    systemCategories, // Make sure we return the systemCategories in the hook result for backward compatibility
    setTemplateUrl,
    handleAddItem,
    handleEditItem,
    handleDeleteItem,
    handleUpdateItem,
    handleSaveNewItem,
    handleSaveExistingItem,
    handleImportTemplate,
    handleImportCSV,
    handleImportCSVContent,
    updateItemSubcategories,
    getItemsByCategory,
    getAllItems,
    getItemsBySubcategory,
    setIsAddDialogOpen,
    setIsEditDialogOpen,
    setIsImportDialogOpen,
    setIsImportCSVDialogOpen
  };
};

export default usePackageManagement;
