// Default subcategories for each system category
export interface DefaultSubcategory {
  name: string;
  systemCategory: string;
  order: number;
  description?: string;
}

export const defaultSubcategories: DefaultSubcategory[] = [
  // Lighting Subcategories
  {
    name: "Recessed",
    systemCategory: "lighting",
    order: 1,
    description: "Recessed ceiling lights and downlights"
  },
  {
    name: "Pendant",
    systemCategory: "lighting",
    order: 2,
    description: "Hanging pendant lights and chandeliers"
  },
  {
    name: "Track",
    systemCategory: "lighting",
    order: 3,
    description: "Track lighting systems and fixtures"
  },
  {
    name: "Smart",
    systemCategory: "lighting",
    order: 4,
    description: "Smart and connected lighting solutions"
  },
  {
    name: "Dimmable",
    systemCategory: "lighting",
    order: 5,
    description: "Dimmable lighting fixtures and controls"
  },
  {
    name: "LED",
    systemCategory: "lighting",
    order: 6,
    description: "LED lighting fixtures and bulbs"
  },
  {
    name: "Outdoor",
    systemCategory: "lighting",
    order: 7,
    description: "Exterior and landscape lighting"
  },
  {
    name: "Under Cabinet",
    systemCategory: "lighting",
    order: 8,
    description: "Under cabinet and task lighting"
  },
  {
    name: "Decorative",
    systemCategory: "lighting",
    order: 9,
    description: "Decorative and accent lighting"
  },

  // Audio Subcategories
  {
    name: "Speakers",
    systemCategory: "audio",
    order: 1,
    description: "All types of speakers and drivers"
  },
  {
    name: "Amplifiers",
    systemCategory: "audio",
    order: 2,
    description: "Audio amplifiers and power systems"
  },
  {
    name: "Receivers",
    systemCategory: "audio",
    order: 3,
    description: "Audio/video receivers and processors"
  },
  {
    name: "Wireless",
    systemCategory: "audio",
    order: 4,
    description: "Wireless audio systems and components"
  },
  {
    name: "Ceiling",
    systemCategory: "audio",
    order: 5,
    description: "In-ceiling and overhead speakers"
  },
  {
    name: "In-Wall",
    systemCategory: "audio",
    order: 6,
    description: "In-wall speakers and components"
  },
  {
    name: "Outdoor",
    systemCategory: "audio",
    order: 7,
    description: "Weather-resistant outdoor audio"
  },
  {
    name: "Subwoofers",
    systemCategory: "audio",
    order: 8,
    description: "Subwoofers and bass management"
  },
  {
    name: "Soundbars",
    systemCategory: "audio",
    order: 9,
    description: "Soundbars and all-in-one systems"
  },

  // Video Subcategories
  {
    name: "Displays",
    systemCategory: "video",
    order: 1,
    description: "TVs, monitors, and display panels"
  },
  {
    name: "Projectors",
    systemCategory: "video",
    order: 2,
    description: "Video projectors and projection systems"
  },
  {
    name: "4K/8K",
    systemCategory: "video",
    order: 3,
    description: "Ultra-high definition video equipment"
  },
  {
    name: "Streaming",
    systemCategory: "video",
    order: 4,
    description: "Streaming devices and media players"
  },
  {
    name: "Cables",
    systemCategory: "video",
    order: 5,
    description: "Video cables and connectivity"
  },
  {
    name: "Mounts",
    systemCategory: "video",
    order: 6,
    description: "TV and display mounting solutions"
  },
  {
    name: "Switchers",
    systemCategory: "video",
    order: 7,
    description: "Video switchers and matrix systems"
  },
  {
    name: "Extenders",
    systemCategory: "video",
    order: 8,
    description: "Video signal extenders and distribution"
  },
  {
    name: "Commercial",
    systemCategory: "video",
    order: 9,
    description: "Commercial-grade video equipment"
  },

  // Security Subcategories
  {
    name: "Cameras",
    systemCategory: "security",
    order: 1,
    description: "Security cameras and surveillance"
  },
  {
    name: "Access Control",
    systemCategory: "security",
    order: 2,
    description: "Door locks and access systems"
  },
  {
    name: "Alarms",
    systemCategory: "security",
    order: 3,
    description: "Alarm systems and sensors"
  },
  {
    name: "Intercoms",
    systemCategory: "security",
    order: 4,
    description: "Intercom and communication systems"
  },
  {
    name: "Motion Sensors",
    systemCategory: "security",
    order: 5,
    description: "Motion detection and sensors"
  },
  {
    name: "Smart Locks",
    systemCategory: "security",
    order: 6,
    description: "Smart and electronic door locks"
  },
  {
    name: "Outdoor",
    systemCategory: "security",
    order: 7,
    description: "Exterior security equipment"
  },
  {
    name: "Indoor",
    systemCategory: "security",
    order: 8,
    description: "Interior security systems"
  },
  {
    name: "Monitoring",
    systemCategory: "security",
    order: 9,
    description: "Security monitoring and recording"
  },

  // Network Subcategories
  {
    name: "Routers",
    systemCategory: "network",
    order: 1,
    description: "Network routers and gateways"
  },
  {
    name: "Switches",
    systemCategory: "network",
    order: 2,
    description: "Network switches and hubs"
  },
  {
    name: "Access Points",
    systemCategory: "network",
    order: 3,
    description: "Wireless access points and WiFi"
  },
  {
    name: "Cables",
    systemCategory: "network",
    order: 4,
    description: "Network cables and connectivity"
  },
  {
    name: "Managed",
    systemCategory: "network",
    order: 5,
    description: "Managed network equipment"
  },
  {
    name: "Unmanaged",
    systemCategory: "network",
    order: 6,
    description: "Unmanaged network equipment"
  },
  {
    name: "PoE",
    systemCategory: "network",
    order: 7,
    description: "Power over Ethernet equipment"
  },
  {
    name: "Fiber",
    systemCategory: "network",
    order: 8,
    description: "Fiber optic network components"
  },
  {
    name: "Enterprise",
    systemCategory: "network",
    order: 9,
    description: "Enterprise-grade network equipment"
  },

  // Shades Subcategories
  {
    name: "Motorized",
    systemCategory: "shades",
    order: 1,
    description: "Motorized and automated shades"
  },
  {
    name: "Manual",
    systemCategory: "shades",
    order: 2,
    description: "Manual operation window treatments"
  },
  {
    name: "Roller",
    systemCategory: "shades",
    order: 3,
    description: "Roller shades and blinds"
  },
  {
    name: "Roman",
    systemCategory: "shades",
    order: 4,
    description: "Roman shades and fabric treatments"
  },
  {
    name: "Cellular",
    systemCategory: "shades",
    order: 5,
    description: "Cellular and honeycomb shades"
  },
  {
    name: "Blackout",
    systemCategory: "shades",
    order: 6,
    description: "Light-blocking and blackout shades"
  },
  {
    name: "Light Filtering",
    systemCategory: "shades",
    order: 7,
    description: "Light filtering window treatments"
  },
  {
    name: "Outdoor",
    systemCategory: "shades",
    order: 8,
    description: "Exterior shades and awnings"
  },
  {
    name: "Smart",
    systemCategory: "shades",
    order: 9,
    description: "Smart and connected window treatments"
  },

  // Control Systems Subcategories
  {
    name: "Processors",
    systemCategory: "controlSystems",
    order: 1,
    description: "Control processors and hubs"
  },
  {
    name: "Touch Panels",
    systemCategory: "controlSystems",
    order: 2,
    description: "Touch screen control interfaces"
  },
  {
    name: "Keypads",
    systemCategory: "controlSystems",
    order: 3,
    description: "Control keypads and buttons"
  },
  {
    name: "Remotes",
    systemCategory: "controlSystems",
    order: 4,
    description: "Remote controls and handheld devices"
  },
  {
    name: "Sensors",
    systemCategory: "controlSystems",
    order: 5,
    description: "Environmental and occupancy sensors"
  },
  {
    name: "Interfaces",
    systemCategory: "controlSystems",
    order: 6,
    description: "Control interfaces and modules"
  },
  {
    name: "Smart Home",
    systemCategory: "controlSystems",
    order: 7,
    description: "Smart home automation systems"
  },
  {
    name: "Commercial",
    systemCategory: "controlSystems",
    order: 8,
    description: "Commercial control systems"
  },
  {
    name: "Integration",
    systemCategory: "controlSystems",
    order: 9,
    description: "System integration components"
  }
];

// Function to get default subcategories for a specific system category
export const getDefaultSubcategoriesForSystem = (systemCategory: string): DefaultSubcategory[] => {
  return defaultSubcategories
    .filter(sub => sub.systemCategory === systemCategory)
    .sort((a, b) => a.order - b.order);
};

// Function to get all system categories that have default subcategories
export const getSystemCategoriesWithDefaults = (): string[] => {
  return [...new Set(defaultSubcategories.map(sub => sub.systemCategory))];
};
