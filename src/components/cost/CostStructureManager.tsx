import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useToast } from '@/hooks/use-toast';
import { PlusCircle, Save, Download, Trash, FileSpreadsheet, Calculator, Package, ShoppingBag } from 'lucide-react';
import { CostStructure, CostSummary, CostCategory, CostLineItem } from '@/models/CostStructure';
import { generateCostSummary, generateSampleCostStructure, generateCostStructureFromRooms } from '@/services/costService';
import { FormData } from '@/types/formTypes';
import ProductSelector from './ProductSelector';

interface CostStructureManagerProps {
  walkthroughData?: FormData;
}

const CostStructureManager: React.FC<CostStructureManagerProps> = ({ walkthroughData }) => {
  const [costStructure, setCostStructure] = useState<CostStructure>(generateSampleCostStructure());
  const [costSummary, setCostSummary] = useState<CostSummary | null>(null);
  const [activeTab, setActiveTab] = useState("structure");
  const [isProductSelectorOpen, setIsProductSelectorOpen] = useState(false);
  const [currentCategoryIndex, setCurrentCategoryIndex] = useState<number | null>(null);
  const { toast } = useToast();
  
  // Use walkthrough data if provided to generate a cost structure
  useEffect(() => {
    if (walkthroughData && walkthroughData.rooms && walkthroughData.rooms.length > 0) {
      const generatedCostStructure = generateCostStructureFromRooms(
        walkthroughData.rooms, 
        walkthroughData.systemCategories,
        walkthroughData.clientInfo?.fullName || "Unnamed Client"
      );
      
      if (generatedCostStructure) {
        setCostStructure(generatedCostStructure);
        toast({
          title: "Cost Structure Generated",
          description: `Created from ${walkthroughData.rooms.length} rooms in the walkthrough`
        });
      }
    }
  }, [walkthroughData, toast]);
  
  useEffect(() => {
    // Generate summary whenever cost structure changes
    if (costStructure) {
      const summary = generateCostSummary(costStructure);
      setCostSummary(summary);
    }
  }, [costStructure]);
  
  const handleStructureNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCostStructure(prev => ({
      ...prev,
      name: e.target.value,
      updatedAt: new Date()
    }));
  };
  
  const handleStructureDescriptionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCostStructure(prev => ({
      ...prev,
      description: e.target.value,
      updatedAt: new Date()
    }));
  };
  
  const handleTaxRateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCostStructure(prev => ({
      ...prev,
      taxRate: parseFloat(e.target.value) || 0,
      updatedAt: new Date()
    }));
  };
  
  const handleMarkupChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCostStructure(prev => ({
      ...prev,
      generalMarkup: parseFloat(e.target.value) || 0,
      updatedAt: new Date()
    }));
  };
  
  const handleItemChange = (
    categoryIndex: number, 
    itemIndex: number, 
    field: keyof CostLineItem, 
    value: string | number
  ) => {
    setCostStructure(prev => {
      const updated = { ...prev };
      const item = updated.categories[categoryIndex].items[itemIndex];
      
      if (typeof value === 'string' && 
         (field === 'unitCost' || field === 'quantity' || field === 'markup' || 
          field === 'discount' || field === 'laborHours' || field === 'laborRate')) {
        // Convert to number for numeric fields
        (item[field] as any) = parseFloat(value) || 0;
      } else {
        (item[field] as any) = value;
      }
      
      updated.updatedAt = new Date();
      return updated;
    });
  };
  
  const handleAddCategory = () => {
    setCostStructure(prev => {
      const newCategory: CostCategory = {
        id: `CAT-${Date.now()}`,
        name: `New Category`,
        description: "",
        items: []
      };
      
      return {
        ...prev,
        categories: [...prev.categories, newCategory],
        updatedAt: new Date()
      };
    });
  };
  
  const handleRemoveCategory = (index: number) => {
    setCostStructure(prev => {
      const updated = { ...prev };
      updated.categories = updated.categories.filter((_, i) => i !== index);
      updated.updatedAt = new Date();
      return updated;
    });
  };
  
  const handleAddItem = (categoryIndex: number) => {
    setCostStructure(prev => {
      const updated = { ...prev };
      const newItem: CostLineItem = {
        id: `ITEM-${Date.now()}`,
        name: "New Item",
        category: "Hardware",
        unitCost: 0,
        quantity: 1,
        unit: "piece"
      };
      
      updated.categories[categoryIndex].items.push(newItem);
      updated.updatedAt = new Date();
      
      return updated;
    });
  };
  
  const handleAddProductsFromCatalog = (categoryIndex: number) => {
    setCurrentCategoryIndex(categoryIndex);
    setIsProductSelectorOpen(true);
  };
  
  const handleProductsSelected = (products: CostLineItem[]) => {
    if (currentCategoryIndex === null || products.length === 0) return;
    
    setCostStructure(prev => {
      const updated = { ...prev };
      // Add all selected products to the category
      updated.categories[currentCategoryIndex].items.push(...products);
      updated.updatedAt = new Date();
      return updated;
    });
    
    toast({
      title: "Products Added",
      description: `Added ${products.length} products to the cost structure`
    });
    
    setCurrentCategoryIndex(null);
  };
  
  const handleRemoveItem = (categoryIndex: number, itemIndex: number) => {
    setCostStructure(prev => {
      const updated = { ...prev };
      updated.categories[categoryIndex].items = 
        updated.categories[categoryIndex].items.filter((_, i) => i !== itemIndex);
      updated.updatedAt = new Date();
      return updated;
    });
  };
  
  const handleCategoryNameChange = (categoryIndex: number, name: string) => {
    setCostStructure(prev => {
      const updated = { ...prev };
      updated.categories[categoryIndex].name = name;
      updated.updatedAt = new Date();
      return updated;
    });
  };
  
  const handleCategoryDescriptionChange = (categoryIndex: number, description: string) => {
    setCostStructure(prev => {
      const updated = { ...prev };
      updated.categories[categoryIndex].description = description;
      updated.updatedAt = new Date();
      return updated;
    });
  };
  
  const handleSave = () => {
    // This would typically save to a database
    // For demo purposes, we're just showing a success message
    
    toast({
      title: "Saved",
      description: "Cost structure saved successfully",
    });
    
    console.log("Saved cost structure:", costStructure);
  };
  
  const handleExportToWeQuote = () => {
    // This would typically export to WeQuote via API
    // For demo purposes, we're just showing a success message
    
    toast({
      title: "Export Initiated",
      description: "Sending data to WeQuote...",
    });
    
    // Simulate API call delay
    setTimeout(() => {
      toast({
        title: "Export Successful",
        description: "Cost structure exported to WeQuote",
      });
    }, 1500);
  };
  
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount);
  };
  
  return (
    <div className="w-full max-w-4xl mx-auto">
      <Card>
        <CardHeader>
          <CardTitle>Cost Structure Manager</CardTitle>
          <CardDescription>
            Create and manage cost structures for quotes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-6 mb-6">
            <div>
              <Label htmlFor="structure-name">Structure Name</Label>
              <Input
                id="structure-name"
                value={costStructure.name}
                onChange={handleStructureNameChange}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="structure-description">Description</Label>
              <Input
                id="structure-description"
                value={costStructure.description || ''}
                onChange={handleStructureDescriptionChange}
                className="mt-1"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-6 mb-6">
            <div>
              <Label htmlFor="tax-rate">Tax Rate (%)</Label>
              <Input
                id="tax-rate"
                type="number"
                min="0"
                step="0.01"
                value={costStructure.taxRate}
                onChange={handleTaxRateChange}
                className="mt-1"
              />
            </div>
            <div>
              <Label htmlFor="markup">General Markup (%)</Label>
              <Input
                id="markup"
                type="number"
                min="0"
                step="0.01"
                value={costStructure.generalMarkup}
                onChange={handleMarkupChange}
                className="mt-1"
              />
            </div>
          </div>
          
          <div className="flex gap-2 mb-6">
            <Button 
              onClick={handleSave}
              className="bg-[#37372f] hover:bg-[#4a4a3f] text-white"
            >
              <Save className="h-4 w-4 mr-2" />
              Save Structure
            </Button>
            <Button 
              variant="outline" 
              onClick={handleExportToWeQuote}
            >
              <FileSpreadsheet className="h-4 w-4 mr-2" />
              Export to WeQuote
            </Button>
          </div>
          
          <Tabs 
            defaultValue="structure" 
            value={activeTab} 
            onValueChange={setActiveTab}
            className="mt-6"
          >
            <TabsList className="grid grid-cols-2 mb-6">
              <TabsTrigger value="structure">Structure</TabsTrigger>
              <TabsTrigger value="summary">Cost Summary</TabsTrigger>
            </TabsList>
            
            <TabsContent value="structure" className="space-y-6">
              {costStructure.categories.map((category, categoryIndex) => (
                <Card key={category.id} className="mb-6">
                  <CardHeader className="bg-gray-50 pb-2">
                    <div className="flex justify-between items-center">
                      <div className="flex-1">
                        <Input
                          value={category.name}
                          onChange={(e) => handleCategoryNameChange(categoryIndex, e.target.value)}
                          className="font-semibold text-lg bg-transparent border-0 focus:border-b focus:ring-0"
                        />
                        <Input
                          value={category.description || ''}
                          onChange={(e) => handleCategoryDescriptionChange(categoryIndex, e.target.value)}
                          placeholder="Category description"
                          className="text-sm text-muted-foreground mt-1 bg-transparent border-0 focus:border-b focus:ring-0"
                        />
                      </div>
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => handleRemoveCategory(categoryIndex)}
                      >
                        <Trash className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-4">
                    <div className="overflow-x-auto">
                      <table className="w-full border-collapse">
                        <thead>
                          <tr className="text-xs text-muted-foreground border-b">
                            <th className="text-left py-2 font-medium">Item</th>
                            <th className="text-left py-2 font-medium">Unit Cost</th>
                            <th className="text-left py-2 font-medium">Qty</th>
                            <th className="text-left py-2 font-medium">Unit</th>
                            <th className="text-left py-2 font-medium">Labor Hrs</th>
                            <th className="text-left py-2 font-medium">Labor Rate</th>
                            <th className="text-left py-2 font-medium">Markup %</th>
                            <th className="text-left py-2 font-medium">Total</th>
                            <th className="py-2 font-medium"></th>
                          </tr>
                        </thead>
                        <tbody>
                          {category.items.map((item, itemIndex) => {
                            // Calculate line item total for display
                            const itemTotal = 
                              item.unitCost * item.quantity + 
                              (item.laborHours && item.laborRate ? item.laborHours * item.laborRate : 0);
                            const withMarkup = item.markup ? 
                              itemTotal * (1 + (item.markup / 100)) : 
                              itemTotal;
                              
                            return (
                              <tr key={item.id} className="border-b">
                                <td className="py-2">
                                  <Input
                                    value={item.name}
                                    onChange={(e) => handleItemChange(
                                      categoryIndex, 
                                      itemIndex, 
                                      'name', 
                                      e.target.value
                                    )}
                                    className="w-full border-0 focus:ring-0 text-sm p-0 h-auto"
                                  />
                                </td>
                                <td className="py-2">
                                  <Input
                                    type="number"
                                    step="0.01"
                                    value={item.unitCost}
                                    onChange={(e) => handleItemChange(
                                      categoryIndex, 
                                      itemIndex, 
                                      'unitCost', 
                                      e.target.value
                                    )}
                                    className="w-24 border-0 focus:ring-0 text-sm p-0 h-auto"
                                  />
                                </td>
                                <td className="py-2">
                                  <Input
                                    type="number"
                                    value={item.quantity}
                                    onChange={(e) => handleItemChange(
                                      categoryIndex, 
                                      itemIndex, 
                                      'quantity', 
                                      e.target.value
                                    )}
                                    className="w-16 border-0 focus:ring-0 text-sm p-0 h-auto"
                                  />
                                </td>
                                <td className="py-2">
                                  <Input
                                    value={item.unit}
                                    onChange={(e) => handleItemChange(
                                      categoryIndex, 
                                      itemIndex, 
                                      'unit', 
                                      e.target.value
                                    )}
                                    className="w-20 border-0 focus:ring-0 text-sm p-0 h-auto"
                                  />
                                </td>
                                <td className="py-2">
                                  <Input
                                    type="number"
                                    step="0.25"
                                    value={item.laborHours || ''}
                                    onChange={(e) => handleItemChange(
                                      categoryIndex, 
                                      itemIndex, 
                                      'laborHours', 
                                      e.target.value
                                    )}
                                    className="w-16 border-0 focus:ring-0 text-sm p-0 h-auto"
                                  />
                                </td>
                                <td className="py-2">
                                  <Input
                                    type="number"
                                    step="0.01"
                                    value={item.laborRate || ''}
                                    onChange={(e) => handleItemChange(
                                      categoryIndex, 
                                      itemIndex, 
                                      'laborRate', 
                                      e.target.value
                                    )}
                                    className="w-20 border-0 focus:ring-0 text-sm p-0 h-auto"
                                  />
                                </td>
                                <td className="py-2">
                                  <Input
                                    type="number"
                                    step="0.1"
                                    value={item.markup || ''}
                                    onChange={(e) => handleItemChange(
                                      categoryIndex, 
                                      itemIndex, 
                                      'markup', 
                                      e.target.value
                                    )}
                                    className="w-16 border-0 focus:ring-0 text-sm p-0 h-auto"
                                  />
                                </td>
                                <td className="py-2 text-right font-medium">
                                  {formatCurrency(withMarkup)}
                                </td>
                                <td className="py-2">
                                  <Button 
                                    variant="ghost" 
                                    size="sm" 
                                    className="h-6 w-6 p-0"
                                    onClick={() => handleRemoveItem(categoryIndex, itemIndex)}
                                  >
                                    <Trash className="h-3 w-3" />
                                  </Button>
                                </td>
                              </tr>
                            );
                          })}
                        </tbody>
                      </table>
                    </div>
                    
                    <div className="flex space-x-2 mt-4">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => handleAddItem(categoryIndex)}
                      >
                        <PlusCircle className="h-4 w-4 mr-2" />
                        Add Item
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleAddProductsFromCatalog(categoryIndex)}
                      >
                        <ShoppingBag className="h-4 w-4 mr-2" />
                        Add from Product Catalog
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
              
              <Button 
                variant="outline" 
                onClick={handleAddCategory}
                className="w-full"
              >
                <PlusCircle className="h-4 w-4 mr-2" />
                Add Category
              </Button>
            </TabsContent>
            
            <TabsContent value="summary">
              {costSummary && (
                <Card>
                  <CardHeader className="bg-gray-50">
                    <CardTitle>Cost Summary</CardTitle>
                    <CardDescription>
                      Summary of all costs and calculations
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pt-6">
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="bg-gray-50 p-4 rounded-md">
                          <p className="text-sm text-muted-foreground">Materials</p>
                          <p className="text-2xl font-semibold">{formatCurrency(costSummary.materials)}</p>
                        </div>
                        <div className="bg-gray-50 p-4 rounded-md">
                          <p className="text-sm text-muted-foreground">Labor</p>
                          <p className="text-2xl font-semibold">{formatCurrency(costSummary.labor)}</p>
                        </div>
                      </div>
                      
                      <div>
                        <h3 className="text-lg font-medium mb-2">Category Breakdown</h3>
                        <div className="space-y-2">
                          {costSummary.breakdown.map((item, index) => (
                            <div key={index} className="flex justify-between border-b pb-2">
                              <span>{item.categoryName}</span>
                              <span className="font-medium">{formatCurrency(item.amount)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                      
                      <div className="border-t pt-4 space-y-2">
                        <div className="flex justify-between">
                          <span>Subtotal</span>
                          <span className="font-medium">{formatCurrency(costSummary.subtotal)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Tax ({costStructure.taxRate}%)</span>
                          <span className="font-medium">{formatCurrency(costSummary.tax)}</span>
                        </div>
                        {costSummary.discount > 0 && (
                          <div className="flex justify-between text-green-600">
                            <span>Discount</span>
                            <span className="font-medium">-{formatCurrency(costSummary.discount)}</span>
                          </div>
                        )}
                        <div className="flex justify-between text-lg font-bold pt-2 border-t">
                          <span>Total</span>
                          <span>{formatCurrency(costSummary.total)}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="bg-gray-50 flex justify-end">
                    <Button className="bg-[#37372f] hover:bg-[#4a4a3f] text-white">
                      <Calculator className="h-4 w-4 mr-2" />
                      Generate Quote
                    </Button>
                  </CardFooter>
                </Card>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      
      {/* Product Selector Dialog */}
      <ProductSelector
        isOpen={isProductSelectorOpen}
        onClose={() => setIsProductSelectorOpen(false)}
        onProductSelect={handleProductsSelected}
      />
    </div>
  );
};

export default CostStructureManager;
