
import React from 'react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { RoomConfig } from '@/types/roomTypes';
import usePackageManagement from '@/hooks/usePackageManagement'; 
import { Package } from 'lucide-react';

interface SystemDetailConfigProps {
  room: RoomConfig;
  updateRoom: (id: string, data: Partial<RoomConfig>) => void;
  // Change type to accept boolean values
  systemCategories: Record<string, boolean | string>;
}

const SystemDetailConfig: React.FC<SystemDetailConfigProps> = ({ 
  room, 
  updateRoom, 
  systemCategories
}) => {
  // Get packages from the usePackageManagement hook
  const { items } = usePackageManagement();

  // Filter systems that are active for this room
  const getActiveSystems = () => {
    const activeSystems = [];
    
    if (room.lighting) activeSystems.push('lighting');
    if (room.shades !== 'none') activeSystems.push('shades');
    if (room.audio !== 'none') activeSystems.push('audio');
    if (room.video !== 'none') activeSystems.push('video');
    if (room.network === 'high density') activeSystems.push('network');
    if (room.surveillance) activeSystems.push('security');
    if (room.control !== 'none') activeSystems.push('controlSystems');
    
    return activeSystems;
  };
  
  const activeSystems = getActiveSystems();
  
  // Get packages for a specific system category
  const getPackagesForSystem = (systemType: string) => {
    return items.filter(pkg => pkg.systemCategory === systemType);
  };

  // Get default package for a system category (if any is marked as default)
  const getDefaultPackage = (systemType: string) => {
    const defaultPkg = items.find(pkg => pkg.systemCategory === systemType && pkg.isDefault);
    return defaultPkg?.id || 'no-default';
  };

  // Get the currently selected package ID for a system
  const getSelectedPackage = (systemType: string) => {
    return room[`${systemType}PackageId`] || getDefaultPackage(systemType);
  };

  // Handle package selection
  const handleSelectPackage = (systemType: string, packageId: string) => {
    updateRoom(room.id, { [`${systemType}PackageId`]: packageId });
  };

  if (activeSystems.length === 0) {
    return (
      <div className="py-3 text-center text-muted-foreground italic">
        No systems selected for this room yet
      </div>
    );
  }
  
  return (
    <div className="space-y-6 mt-4">
      <h3 className="text-lg font-semibold border-b pb-2">System Configuration</h3>
      
      {activeSystems.map(systemType => (
        <div key={systemType} className="space-y-4 py-4 border-b border-muted last:border-0">
          <div className="flex items-center justify-between">
            <h4 className="text-md font-medium">{systemCategories[systemType]}</h4>
          </div>

          {/* Package selection for each active system */}
          <div className="space-y-2">
            <Label htmlFor={`${systemType}-package-${room.id}`} className="flex items-center gap-1.5">
              <Package className="h-4 w-4" />
              <span>Package Selection</span>
            </Label>
            
            <Select
              value={getSelectedPackage(systemType)}
              onValueChange={(value) => handleSelectPackage(systemType, value)}
            >
              <SelectTrigger id={`${systemType}-package-${room.id}`} className="w-full">
                <SelectValue placeholder={`Select a ${systemCategories[systemType]} package`} />
              </SelectTrigger>
              <SelectContent>
                {getPackagesForSystem(systemType).map(pkg => (
                  <SelectItem key={pkg.id} value={pkg.id}>
                    {pkg.name} {pkg.isDefault ? "(Default)" : ""}
                  </SelectItem>
                ))}
                {getPackagesForSystem(systemType).length === 0 && (
                  <SelectItem value="no-packages" disabled>
                    No packages available
                  </SelectItem>
                )}
              </SelectContent>
            </Select>
          </div>

          {/* System-specific configuration options */}
          {systemType === 'lighting' && (
            <div className="space-y-2">
              <Label htmlFor={`lighting-notes-${room.id}`}>Lighting Notes</Label>
              <Textarea
                id={`lighting-notes-${room.id}`}
                value={room.lightingNotes || ''}
                onChange={(e) => updateRoom(room.id, { lightingNotes: e.target.value })}
                placeholder="Notes for lighting (fixtures, zones, scenes...)"
              />
            </div>
          )}
          
          {systemType === 'shades' && (
            <div className="space-y-2">
              <Label htmlFor={`shades-${room.id}`}>Shade Type</Label>
              <Select
                value={room.shades}
                onValueChange={(value) => updateRoom(room.id, { shades: value as "none" | "manual" | "motorized" })}
              >
                <SelectTrigger id={`shades-${room.id}`}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="manual">Manual</SelectItem>
                  <SelectItem value="motorized">Motorized</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
          
          {systemType === 'audio' && (
            <div className="space-y-2">
              <Label htmlFor={`audio-${room.id}`}>Audio System</Label>
              <Select
                value={room.audio}
                onValueChange={(value) => updateRoom(room.id, { audio: value as "none" | "hidden" | "in-ceiling" })}
              >
                <SelectTrigger id={`audio-${room.id}`}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hidden">Hidden Speakers</SelectItem>
                  <SelectItem value="in-ceiling">In-Ceiling Speakers</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
          
          {systemType === 'video' && (
            <div className="space-y-2">
              <Label htmlFor={`video-${room.id}`}>Video System</Label>
              <Select
                value={room.video}
                onValueChange={(value) => updateRoom(room.id, { video: value as "none" | "display" | "projector" })}
              >
                <SelectTrigger id={`video-${room.id}`}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="display">Display/TV</SelectItem>
                  <SelectItem value="projector">Projector</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
          
          {systemType === 'network' && (
            <div className="space-y-2">
              <Label>Network Configuration</Label>
              <p className="text-sm text-muted-foreground">
                High-density networking configured for this room
              </p>
            </div>
          )}
          
          {systemType === 'security' && (
            <div className="space-y-2">
              <Label>Surveillance</Label>
              <p className="text-sm text-muted-foreground">
                Surveillance system configured for this room
              </p>
            </div>
          )}
          
          {systemType === 'controlSystems' && (
            <div className="space-y-2">
              <Label htmlFor={`control-${room.id}`}>Control Options</Label>
              <Select
                value={room.control}
                onValueChange={(value) => updateRoom(room.id, { control: value as "none" | "wall keypad" | "remote" | "app" })}
              >
                <SelectTrigger id={`control-${room.id}`}>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="wall keypad">Wall Keypad</SelectItem>
                  <SelectItem value="remote">Remote</SelectItem>
                  <SelectItem value="app">Mobile App</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}
        </div>
      ))}
    </div>
  );
};

export default SystemDetailConfig;
