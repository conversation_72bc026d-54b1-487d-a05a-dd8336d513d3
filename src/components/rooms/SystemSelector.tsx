
import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Label } from '@/components/ui/label';
import { RoomConfig } from '@/types/roomTypes';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { 
  LightbulbIcon, 
  PanelTopIcon, 
  SpeakerIcon, 
  MonitorIcon, 
  WifiIcon, 
  ShieldIcon,
  MonitorSmartphone // Using MonitorSmartphone instead of RemoteControl which doesn't exist
} from 'lucide-react';
import SystemDetailConfig from './SystemDetailConfig';
import usePackageManagement from '@/hooks/usePackageManagement';

interface SystemSelectorProps {
  roomSettings: RoomConfig;
  roomId: string;
  onUpdate: (id: string, data: Partial<RoomConfig>) => void;
}

const SystemSelector: React.FC<SystemSelectorProps> = ({ 
  roomSettings,
  roomId,
  onUpdate 
}) => {
  const [activeTab, setActiveTab] = useState<string>("systems");
  const [isLightingEnabled, setIsLightingEnabled] = useState(roomSettings.lighting || false);
  const [shadesOption, setShadesOption] = useState<"none" | "manual" | "motorized">(roomSettings.shades || 'none');
  const [audioOption, setAudioOption] = useState<"none" | "hidden" | "in-ceiling">(roomSettings.audio || 'none');
  const [videoOption, setVideoOption] = useState<"none" | "display" | "projector">(roomSettings.video || 'none');
  const [isNetworkEnabled, setIsNetworkEnabled] = useState(roomSettings.network === 'high density' || false);
  const [isSecurityEnabled, setIsSecurityEnabled] = useState(roomSettings.surveillance || false);
  const [controlOption, setControlOption] = useState<"none" | "wall keypad" | "remote" | "app">(roomSettings.control || 'none');

  useEffect(() => {
    setIsLightingEnabled(roomSettings.lighting || false);
    setShadesOption(roomSettings.shades || 'none');
    setAudioOption(roomSettings.audio || 'none');
    setVideoOption(roomSettings.video || 'none');
    setIsNetworkEnabled(roomSettings.network === 'high density' || false);
    setIsSecurityEnabled(roomSettings.surveillance || false);
    setControlOption(roomSettings.control || 'none');
  }, [roomSettings]);

  const handleSystemToggle = (systemType: string, enabled: boolean) => {
    switch (systemType) {
      case 'lighting':
        onUpdate(roomId, { lighting: enabled });
        break;
      case 'network':
        onUpdate(roomId, { network: enabled ? 'high density' : 'standard' });
        break;
      case 'security':
        onUpdate(roomId, { surveillance: enabled });
        break;
      default:
        break;
    }
  };

  const handleSystemOptionChange = (systemType: string, option: string) => {
    switch (systemType) {
      case 'shades':
        // Safe type casting with validation
        if (option === "none" || option === "manual" || option === "motorized") {
          setShadesOption(option);
          onUpdate(roomId, { shades: option });
        }
        break;
      case 'audio':
        // Safe type casting with validation
        if (option === "none" || option === "hidden" || option === "in-ceiling") {
          setAudioOption(option);
          onUpdate(roomId, { audio: option });
        }
        break;
      case 'video':
        // Safe type casting with validation
        if (option === "none" || option === "display" || option === "projector") {
          setVideoOption(option);
          onUpdate(roomId, { video: option });
        }
        break;
      case 'control':
        // Safe type casting with validation
        if (option === "none" || option === "wall keypad" || option === "remote" || option === "app") {
          setControlOption(option);
          onUpdate(roomId, { control: option });
        }
        break;
      default:
        onUpdate(roomId, { [systemType]: option });
        break;
    }
  };

  // Get packages for each system type
  const { items, systemCategories } = usePackageManagement();

  // Map system types to display names
  const systemCategoriesMap = {
    lighting: "Lighting",
    shades: "Shades",
    audio: "Audio",
    video: "Video",
    network: "Network",
    security: "Security",
    controlSystems: "Control Systems"
  };

  const renderSystemOptions = (systemType: string) => {
    let options = [];
    let selectedOption = '';
    let handleOptionChange = (value: string) => {};

    switch (systemType) {
      case 'shades':
        options = [
          { value: 'none', label: 'None' },
          { value: 'manual', label: 'Manual' },
          { value: 'motorized', label: 'Motorized', badge: 'recommended' },
        ];
        selectedOption = shadesOption;
        handleOptionChange = (value: string) => handleSystemOptionChange('shades', value);
        break;
      case 'audio':
        options = [
          { value: 'none', label: 'None' },
          { value: 'hidden', label: 'Hidden Speakers' },
          { value: 'in-ceiling', label: 'In-Ceiling Speakers', badge: 'recommended' },
        ];
        selectedOption = audioOption;
        handleOptionChange = (value: string) => handleSystemOptionChange('audio', value);
        break;
      case 'video':
        options = [
          { value: 'none', label: 'None' },
          { value: 'display', label: 'Display/TV' },
          { value: 'projector', label: 'Projector', badge: 'recommended' },
        ];
        selectedOption = videoOption;
        handleOptionChange = (value: string) => handleSystemOptionChange('video', value);
        break;
      case 'control':
        options = [
          { value: 'none', label: 'None' },
          { value: 'wall keypad', label: 'Wall Keypad' },
          { value: 'remote', label: 'Remote' },
          { value: 'app', label: 'Mobile App', badge: 'recommended' },
        ];
        selectedOption = controlOption;
        handleOptionChange = (value: string) => handleSystemOptionChange('control', value);
        break;
      default:
        break;
    }

    return (
      <div className="space-y-4 mt-1">
        <RadioGroup 
          value={selectedOption} 
          onValueChange={handleOptionChange}
          className="flex flex-col space-y-1"
        >
          {options.map(option => (
            <div key={option.value} className="flex items-center space-x-2">
              <RadioGroupItem value={option.value} id={`${systemType}-${option.value}`} />
              <Label 
                htmlFor={`${systemType}-${option.value}`}
                className="flex items-center"
              >
                {option.label}
                {option.badge && (
                  <Badge 
                    variant="outline" 
                    className="ml-2 text-xs"
                  >
                    {option.badge === 'recommended' 
                      ? 'Recommended' 
                      : option.value}
                  </Badge>
                )}
              </Label>
            </div>
          ))}
        </RadioGroup>
      </div>
    );
  };
  
  return (
    <div className="space-y-6">
      <Tabs defaultValue="systems" className="w-full" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="systems">Systems</TabsTrigger>
          <TabsTrigger value="details">Details</TabsTrigger>
        </TabsList>
        <TabsContent value="systems" className="outline-none">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="lighting">Lighting</Label>
                <Switch id="lighting" checked={isLightingEnabled} onCheckedChange={(checked) => {
                  setIsLightingEnabled(checked);
                  handleSystemToggle('lighting', checked);
                }} />
              </div>
              {isLightingEnabled && (
                <div className="ml-6">
                  {renderSystemOptions('lighting')}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="shades">Shades</Label>
              </div>
              {renderSystemOptions('shades')}
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="audio">Audio</Label>
              </div>
              {renderSystemOptions('audio')}
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="video">Video</Label>
              </div>
              {renderSystemOptions('video')}
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="network">Network</Label>
                <Switch id="network" checked={isNetworkEnabled} onCheckedChange={(checked) => {
                  setIsNetworkEnabled(checked);
                  handleSystemToggle('network', checked);
                }} />
              </div>
              {isNetworkEnabled && (
                <div className="ml-6">
                  <p className="text-sm text-muted-foreground">High-density networking configured for this room</p>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="security">Surveillance</Label>
                <Switch id="security" checked={isSecurityEnabled} onCheckedChange={(checked) => {
                  setIsSecurityEnabled(checked);
                  handleSystemToggle('security', checked);
                }} />
              </div>
              {isSecurityEnabled && (
                <div className="ml-6">
                  <p className="text-sm text-muted-foreground">Surveillance system configured for this room</p>
                </div>
              )}
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <Label htmlFor="control">Control</Label>
              </div>
              {renderSystemOptions('control')}
            </div>
          </div>
        </TabsContent>
        <TabsContent value="details" className="outline-none">
          <SystemDetailConfig 
            room={roomSettings} 
            updateRoom={(id, data) => onUpdate(id, data)} 
            systemCategories={systemCategories} 
          />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SystemSelector;
