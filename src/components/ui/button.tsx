
import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm",
        destructive:
          "bg-destructive text-destructive-foreground hover:bg-destructive/90 shadow-sm",
        outline:
          "border border-input bg-background hover:bg-accent hover:text-accent-foreground shadow-sm",
        secondary:
          "bg-secondary text-secondary-foreground hover:bg-secondary/80 shadow-sm",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
        accent: "bg-accent text-accent-foreground hover:bg-accent/90 shadow-sm",
        soft: "bg-primary/10 text-primary hover:bg-primary/20 border border-primary/20",
        pill: "rounded-full px-4 py-1 text-xs shadow-sm border border-input bg-background hover:bg-accent/50 hover:text-accent-foreground",
        glass: "bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 text-foreground shadow-sm",
        gradient: "bg-gradient-to-r from-primary to-secondary text-white border-none shadow hover:shadow-lg transition-all duration-200 transform hover:-translate-y-[1px] font-medium",
        modern: "rounded-md bg-gradient-to-r from-primary/90 to-primary border-none text-white shadow hover:shadow-md transition-all duration-200 hover:translate-y-[-1px]",
        square: "aspect-square p-0 flex items-center justify-center",
        icon: "p-2 rounded-full w-10 h-10 flex items-center justify-center",
        "icon-gradient": "p-2 rounded-full w-10 h-10 flex items-center justify-center bg-gradient-to-r from-primary to-secondary text-white",
        "default-toggle": "bg-transparent hover:bg-muted/20 data-[state=on]:bg-gradient-to-r data-[state=on]:from-primary/80 data-[state=on]:to-secondary/80 data-[state=on]:text-white shadow-sm border border-muted transition-all duration-200",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3 text-xs",
        lg: "h-11 rounded-md px-8 text-base",
        icon: "h-10 w-10",
        xl: "h-12 rounded-md px-10 text-base",
        full: "h-10 px-4 py-2 w-full",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }
