import React, { useState, useEffect } from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { supabase } from '@/integrations/supabase/client';
import { initializeDefaultSubcategories } from '@/utils/initializeDefaultSubcategories';

interface Subcategory {
  id: string;
  name: string;
  systemCategory: string;
}

interface SubcategoryFilterProps {
  systemCategory: string;
  selectedSubcategory: string | null;
  onSubcategoryChange: (subcategory: string | null) => void;
}

const SubcategoryFilter: React.FC<SubcategoryFilterProps> = ({
  systemCategory,
  selectedSubcategory,
  onSubcategoryChange
}) => {
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (systemCategory) {
      fetchSubcategories();
    } else {
      setSubcategories([]);
    }
  }, [systemCategory]);

  const fetchSubcategories = async () => {
    try {
      setIsLoading(true);

      // Initialize default subcategories if needed
      await initializeDefaultSubcategories();

      const { data, error } = await supabase
        .from('product_subcategories')
        .select('*')
        .eq('system_category', systemCategory)
        .order('order_index');

      if (error) {
        console.error('Error fetching subcategories:', error);
        return;
      }

      const mappedSubcategories: Subcategory[] = (data || []).map(sub => ({
        id: sub.id,
        name: sub.name,
        systemCategory: sub.system_category
      }));

      setSubcategories(mappedSubcategories);
    } catch (error) {
      console.error('Error fetching subcategories:', error);
    } finally {
      setIsLoading(false);
    }
  };

  if (!systemCategory || subcategories.length === 0) {
    return null;
  }

  return (
    <Select
      value={selectedSubcategory || 'all'}
      onValueChange={(value) => onSubcategoryChange(value === 'all' ? null : value)}
    >
      <SelectTrigger className="w-[200px]">
        <SelectValue placeholder="All Subcategories" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all">All Subcategories</SelectItem>
        {subcategories.map(subcategory => (
          <SelectItem key={subcategory.id} value={subcategory.name}>
            {subcategory.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
};

export default SubcategoryFilter;
