import React, { useState, useEffect } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Edit2, Trash2, GripVertical, Save, X, Star, Lock } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { systemCategories } from '@/hooks/usePackageManagement';
import { initializeDefaultSubcategories } from '@/utils/initializeDefaultSubcategories';

interface Subcategory {
  id: string;
  name: string;
  systemCategory: string;
  order: number;
  productCount?: number;
  isDefault?: boolean;
  description?: string;
}

interface SubcategoryManagerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubcategoriesUpdated?: () => void;
}

const SubcategoryManager: React.FC<SubcategoryManagerProps> = ({
  open,
  onOpenChange,
  onSubcategoriesUpdated
}) => {
  const [subcategories, setSubcategories] = useState<Subcategory[]>([]);
  const [selectedSystemCategory, setSelectedSystemCategory] = useState<string>('lighting');
  const [newSubcategoryName, setNewSubcategoryName] = useState('');
  const [editingSubcategory, setEditingSubcategory] = useState<string | null>(null);
  const [editingName, setEditingName] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (open) {
      fetchSubcategories();
    }
  }, [open]);

  const fetchSubcategories = async () => {
    try {
      setIsLoading(true);

      // Initialize default subcategories if needed
      await initializeDefaultSubcategories();

      // Fetch custom subcategories from database
      const { data: customSubcategories, error: customError } = await supabase
        .from('product_subcategories')
        .select('*')
        .order('system_category, order_index');

      if (customError) {
        console.error('Error fetching custom subcategories:', customError);
        toast.error('Failed to load subcategories');
        return;
      }

      // Get product counts for each subcategory
      const { data: productCounts, error: countError } = await supabase
        .from('product_catalog_items')
        .select('subcategories');

      if (countError) {
        console.error('Error fetching product counts:', countError);
      }

      // Count products for each subcategory
      const subcategoryCounts: Record<string, number> = {};
      if (productCounts) {
        productCounts.forEach(product => {
          if (product.subcategories) {
            product.subcategories.forEach(subcategory => {
              subcategoryCounts[subcategory] = (subcategoryCounts[subcategory] || 0) + 1;
            });
          }
        });
      }

      // Map database subcategories to our format
      const mappedSubcategories: Subcategory[] = (customSubcategories || []).map(sub => ({
        id: sub.id,
        name: sub.name,
        systemCategory: sub.system_category,
        order: sub.order_index || 0,
        productCount: subcategoryCounts[sub.name] || 0,
        isDefault: sub.is_default || false,
        description: sub.description || undefined
      }));

      setSubcategories(mappedSubcategories);
    } catch (error) {
      console.error('Error fetching subcategories:', error);
      toast.error('Failed to load subcategories');
    } finally {
      setIsLoading(false);
    }
  };

  const getSubcategoriesForSystem = (systemCategory: string) => {
    return subcategories
      .filter(sub => sub.systemCategory === systemCategory)
      .sort((a, b) => a.order - b.order);
  };

  const handleAddSubcategory = async () => {
    if (!newSubcategoryName.trim()) {
      toast.error('Please enter a subcategory name');
      return;
    }

    // Check if subcategory already exists for this system
    const existingSubcategory = subcategories.find(
      sub => sub.systemCategory === selectedSystemCategory &&
             sub.name.toLowerCase() === newSubcategoryName.trim().toLowerCase()
    );

    if (existingSubcategory) {
      toast.error('A subcategory with this name already exists for this system');
      return;
    }

    try {
      const maxOrder = Math.max(
        ...getSubcategoriesForSystem(selectedSystemCategory).map(sub => sub.order),
        -1
      );

      const newSubcategory = {
        id: crypto.randomUUID(),
        name: newSubcategoryName.trim(),
        system_category: selectedSystemCategory,
        order_index: maxOrder + 1
      };

      const { error } = await supabase
        .from('product_subcategories')
        .insert([newSubcategory]);

      if (error) {
        console.error('Error creating subcategory:', error);
        toast.error('Failed to create subcategory');
        return;
      }

      // Add to local state
      setSubcategories(prev => [...prev, {
        id: newSubcategory.id,
        name: newSubcategory.name,
        systemCategory: newSubcategory.system_category,
        order: newSubcategory.order_index,
        productCount: 0
      }]);

      setNewSubcategoryName('');
      toast.success('Subcategory created successfully');
      onSubcategoriesUpdated?.();
    } catch (error) {
      console.error('Error creating subcategory:', error);
      toast.error('Failed to create subcategory');
    }
  };

  const handleEditSubcategory = async (subcategoryId: string) => {
    if (!editingName.trim()) {
      toast.error('Please enter a subcategory name');
      return;
    }

    const subcategory = subcategories.find(sub => sub.id === subcategoryId);
    if (!subcategory) return;

    // Check if name already exists for this system (excluding current subcategory)
    const existingSubcategory = subcategories.find(
      sub => sub.systemCategory === subcategory.systemCategory &&
             sub.id !== subcategoryId &&
             sub.name.toLowerCase() === editingName.trim().toLowerCase()
    );

    if (existingSubcategory) {
      toast.error('A subcategory with this name already exists for this system');
      return;
    }

    try {
      const { error } = await supabase
        .from('product_subcategories')
        .update({ name: editingName.trim() })
        .eq('id', subcategoryId);

      if (error) {
        console.error('Error updating subcategory:', error);
        toast.error('Failed to update subcategory');
        return;
      }

      // Update local state
      setSubcategories(prev => prev.map(sub =>
        sub.id === subcategoryId
          ? { ...sub, name: editingName.trim() }
          : sub
      ));

      setEditingSubcategory(null);
      setEditingName('');
      toast.success('Subcategory updated successfully');
      onSubcategoriesUpdated?.();
    } catch (error) {
      console.error('Error updating subcategory:', error);
      toast.error('Failed to update subcategory');
    }
  };

  const handleDeleteSubcategory = async (subcategoryId: string) => {
    const subcategory = subcategories.find(sub => sub.id === subcategoryId);
    if (!subcategory) return;

    // Prevent deletion of default subcategories
    if (subcategory.isDefault) {
      toast.error(`Cannot delete "${subcategory.name}" because it is a system default subcategory. You can edit its name but not delete it.`);
      return;
    }

    // Check if subcategory is used by any products
    if (subcategory.productCount && subcategory.productCount > 0) {
      toast.error(`Cannot delete subcategory "${subcategory.name}" because it is assigned to ${subcategory.productCount} product(s)`);
      return;
    }

    if (!confirm(`Are you sure you want to delete the subcategory "${subcategory.name}"?`)) {
      return;
    }

    try {
      const { error } = await supabase
        .from('product_subcategories')
        .delete()
        .eq('id', subcategoryId);

      if (error) {
        console.error('Error deleting subcategory:', error);
        toast.error('Failed to delete subcategory');
        return;
      }

      // Remove from local state
      setSubcategories(prev => prev.filter(sub => sub.id !== subcategoryId));
      toast.success('Subcategory deleted successfully');
      onSubcategoriesUpdated?.();
    } catch (error) {
      console.error('Error deleting subcategory:', error);
      toast.error('Failed to delete subcategory');
    }
  };

  const startEditing = (subcategory: Subcategory) => {
    setEditingSubcategory(subcategory.id);
    setEditingName(subcategory.name);
  };

  const cancelEditing = () => {
    setEditingSubcategory(null);
    setEditingName('');
  };

  const getCategoryLabel = (category: string) => {
    return systemCategories[category] || category;
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Manage Subcategories</DialogTitle>
          <DialogDescription>
            Create and manage subcategories for better product organization.
            Subcategories help you organize products within each system category.
            System default subcategories are provided for common product types and can be edited but not deleted.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Add New Subcategory Section */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Add New Subcategory</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="system-category">System Category</Label>
                  <Select
                    value={selectedSystemCategory}
                    onValueChange={setSelectedSystemCategory}
                  >
                    <SelectTrigger id="system-category">
                      <SelectValue placeholder="Select system category" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(systemCategories).map(([key, label]) => (
                        <SelectItem key={key} value={key}>{label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subcategory-name">Subcategory Name</Label>
                  <div className="flex gap-2">
                    <Input
                      id="subcategory-name"
                      value={newSubcategoryName}
                      onChange={(e) => setNewSubcategoryName(e.target.value)}
                      placeholder="Enter subcategory name"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handleAddSubcategory();
                        }
                      }}
                    />
                    <Button onClick={handleAddSubcategory} disabled={isLoading}>
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Existing Subcategories by System */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Existing Subcategories</CardTitle>
            </CardHeader>
            <CardContent>
              <Tabs value={selectedSystemCategory} onValueChange={setSelectedSystemCategory}>
                <TabsList className="grid w-full grid-cols-4 lg:grid-cols-7">
                  {Object.entries(systemCategories).map(([key, label]) => (
                    <TabsTrigger key={key} value={key} className="text-xs">
                      {label}
                    </TabsTrigger>
                  ))}
                </TabsList>

                {Object.keys(systemCategories).map(systemKey => (
                  <TabsContent key={systemKey} value={systemKey} className="mt-4">
                    <div className="space-y-2">
                      {getSubcategoriesForSystem(systemKey).length === 0 ? (
                        <p className="text-sm text-muted-foreground text-center py-8">
                          No subcategories created for {getCategoryLabel(systemKey)} yet.
                        </p>
                      ) : (
                        getSubcategoriesForSystem(systemKey).map(subcategory => (
                          <div
                            key={subcategory.id}
                            className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                          >
                            <div className="flex items-center gap-3">
                              <GripVertical className="h-4 w-4 text-gray-400" />
                              {editingSubcategory === subcategory.id ? (
                                <div className="flex items-center gap-2">
                                  <Input
                                    value={editingName}
                                    onChange={(e) => setEditingName(e.target.value)}
                                    className="w-48"
                                    onKeyDown={(e) => {
                                      if (e.key === 'Enter') {
                                        e.preventDefault();
                                        handleEditSubcategory(subcategory.id);
                                      } else if (e.key === 'Escape') {
                                        cancelEditing();
                                      }
                                    }}
                                    autoFocus
                                  />
                                  <Button
                                    size="sm"
                                    onClick={() => handleEditSubcategory(subcategory.id)}
                                  >
                                    <Save className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="outline"
                                    onClick={cancelEditing}
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                </div>
                              ) : (
                                <>
                                  <div className="flex items-center gap-2">
                                    {subcategory.isDefault && (
                                      <Star className="h-3 w-3 text-amber-500" title="System Default" />
                                    )}
                                    <span className="font-medium">{subcategory.name}</span>
                                    {subcategory.isDefault && (
                                      <Badge variant="outline" className="text-xs border-amber-200 text-amber-700">
                                        Default
                                      </Badge>
                                    )}
                                  </div>
                                  <Badge variant="secondary" className="text-xs">
                                    {subcategory.productCount || 0} products
                                  </Badge>
                                  {subcategory.description && (
                                    <p className="text-xs text-muted-foreground mt-1">
                                      {subcategory.description}
                                    </p>
                                  )}
                                </>
                              )}
                            </div>

                            {editingSubcategory !== subcategory.id && (
                              <div className="flex items-center gap-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => startEditing(subcategory)}
                                >
                                  <Edit2 className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={() => handleDeleteSubcategory(subcategory.id)}
                                  disabled={
                                    subcategory.isDefault ||
                                    (subcategory.productCount && subcategory.productCount > 0)
                                  }
                                  title={
                                    subcategory.isDefault
                                      ? "Cannot delete system default subcategories"
                                      : subcategory.productCount && subcategory.productCount > 0
                                      ? `Cannot delete - used by ${subcategory.productCount} products`
                                      : "Delete subcategory"
                                  }
                                >
                                  {subcategory.isDefault ? (
                                    <Lock className="h-3 w-3" />
                                  ) : (
                                    <Trash2 className="h-3 w-3" />
                                  )}
                                </Button>
                              </div>
                            )}
                          </div>
                        ))
                      )}
                    </div>
                  </TabsContent>
                ))}
              </Tabs>
            </CardContent>
          </Card>
        </div>

        <div className="flex justify-end pt-4">
          <Button onClick={() => onOpenChange(false)}>
            Done
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SubcategoryManager;
