import React, { useState, useEffect } from 'react';
import { useFormContext } from '@/context/FormContext';
import FormNavigation from '../FormNavigation';
import { 
  Lightbulb, 
  PanelTop, 
  Speaker, 
  Monitor, 
  Wifi, 
  ShieldCheck, 
  Radio,
  CheckCircle2,
  Package
} from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '../ui/checkbox';
import { Progress } from '../ui/progress';
import { Tabs, TabsList, TabsTrigger, TabsContent } from '../ui/tabs';
import { Alert, AlertDescription } from '../ui/alert';
import { Badge } from '../ui/badge';
import { getDefaultWeQuotePackages } from '@/services/zapierService';
import WeQuotePackageManager from '@/components/integrations/WeQuotePackageManager';

interface SystemSelectionStepProps {
  hasSidebar?: boolean;
}

// Define the structured subcategory data type
interface SubcategoryData {
  name: string;
  enabled: boolean;
}

// Update the type for SYSTEM_SUBCATEGORIES export
interface SystemSubcategories {
  lighting: Record<string, SubcategoryData>;
  shades: Record<string, SubcategoryData>;
  audio: Record<string, SubcategoryData>;
  video: Record<string, SubcategoryData>;
  network: Record<string, SubcategoryData>;
  security: Record<string, SubcategoryData>;
  controlSystems: Record<string, SubcategoryData>;
}

// Define system subcategories with properly typed structure
export const SYSTEM_SUBCATEGORIES: SystemSubcategories = {
  lighting: {
    lightingControl: { name: "Lighting Control", enabled: false },
    lightingFixtures: { name: "Lighting Fixtures", enabled: false },
    colorChanging: { name: "Color Changing", enabled: false },
    circadianRhythm: { name: "Circadian Rhythm", enabled: false },
  },
  shades: {
    rollerShades: { name: "Roller Shades", enabled: false },
    romanShades: { name: "Roman Shades", enabled: false },
    drapery: { name: "Drapery", enabled: false },
    venetianBlinds: { name: "Venetian Blinds", enabled: false },
  },
  audio: {
    multiRoomAudio: { name: "Multi-Room Audio", enabled: false },
    homeTheater: { name: "Home Theater", enabled: false },
    outdoorAV: { name: "Outdoor AV", enabled: false },
    streamingServices: { name: "Streaming Services", enabled: false },
  },
  video: {
    homeTheater: { name: "Home Theater", enabled: false },
    streamingServices: { name: "Streaming Services", enabled: false },
  },
  network: {
    wifi: { name: "WiFi", enabled: false },
    wiredNetwork: { name: "Wired Network", enabled: false },
    networkSecurity: { name: "Network Security", enabled: false },
    remoteAccess: { name: "Remote Access", enabled: false },
  },
  security: {
    cameras: { name: "Cameras", enabled: false },
    videoDoorbell: { name: "Video Doorbell", enabled: false },
    recordingStorage: { name: "Recording Storage", enabled: false },
    motionDetection: { name: "Motion Detection", enabled: false },
  },
  controlSystems: {
    touchscreens: { name: "Touchscreens", enabled: false },
    mobileApp: { name: "Mobile App", enabled: false },
    voiceControl: { name: "Voice Control", enabled: false },
    keypads: { name: "Keypads", enabled: false },
  }
};

const SystemSelectionStep: React.FC<SystemSelectionStepProps> = ({ hasSidebar }) => {
  const { formData, setFormData } = useFormContext();
  const { systemCategories } = formData;
  const [activeTab, setActiveTab] = useState<string>("systems");
  const [defaultPackageCount, setDefaultPackageCount] = useState(0);
  const [systemSubcategories, setSystemSubcategories] = useState<SystemSubcategories>(
    // Initialize with deep copy of standard subcategories
    JSON.parse(JSON.stringify(SYSTEM_SUBCATEGORIES))
  );

  useEffect(() => {
    // Check for default packages
    const fetchDefaultPackages = async () => {
      const defaultPackages = await getDefaultWeQuotePackages();
      setDefaultPackageCount(defaultPackages.length);
    };
    
    fetchDefaultPackages();
  }, []);

  const updateSystemCategory = (key: keyof typeof systemCategories, value: boolean) => {
    setFormData((prev) => ({
      ...prev,
      systemCategories: {
        ...prev.systemCategories,
        [key]: value,
      },
    }));
  };

  const updateSubcategory = (
    system: string, 
    subcategory: string, 
    checked: boolean
  ) => {
    setSystemSubcategories(prev => ({
      ...prev,
      [system]: {
        ...prev[system as keyof typeof prev],
        [subcategory]: {
          ...prev[system as keyof typeof prev][subcategory],
          enabled: checked
        }
      }
    }));
    
    // Store the subcategory selection in formData for use with products
    const subcategoryKey = `${system}_${subcategory}`;
    setFormData(prev => ({
      ...prev,
      selectedSubcategories: {
        ...(prev.selectedSubcategories || {}),
        [subcategoryKey]: checked
      }
    }));
  };

  const getCategoryIcon = (key: string) => {
    switch (key) {
      case 'lighting': return <Lightbulb className="h-6 w-6" />;
      case 'shades': return <PanelTop className="h-6 w-6" />;
      case 'audio': 
      case 'video': return <Monitor className="h-6 w-6" />;
      case 'network': return <Wifi className="h-6 w-6" />;
      case 'security': return <ShieldCheck className="h-6 w-6" />;
      case 'controlSystems': return <Radio className="h-6 w-6" />;
      default: return null;
    }
  };

  const getCategoryLabel = (key: string) => {
    switch (key) {
      case 'lighting': return 'Lighting System';
      case 'shades': return 'Shading System';
      case 'audio': return 'Audio System';
      case 'video': return 'Video System';
      case 'network': return 'Networking System';
      case 'security': return 'Surveillance System';
      case 'controlSystems': return 'Control System';
      default: return key;
    }
  };

  const renderSystemCard = (systemKey: string, subcategories: Record<string, SubcategoryData>) => {
    // Skip video as it's merged with audio in the UI
    if (systemKey === 'video') return null;
    
    const icon = getCategoryIcon(systemKey);
    const label = getCategoryLabel(systemKey);
    const isEnabled = systemCategories[systemKey as keyof typeof systemCategories];
    
    // Special case for audio/video which are combined in the UI
    const isAudioVideo = systemKey === 'audio';
    const combinedLabel = isAudioVideo ? 'Audio/Video System' : label;
    
    // For audio, we need to include video subcategories too
    const displayedSubcategories = isAudioVideo 
      ? { ...subcategories, ...systemSubcategories.video }
      : subcategories;
    
    // Create subcategory items - split into two columns
    const subcategoryEntries = Object.entries(displayedSubcategories);
    const leftColumnItems = subcategoryEntries.slice(0, Math.ceil(subcategoryEntries.length / 2));
    const rightColumnItems = subcategoryEntries.slice(Math.ceil(subcategoryEntries.length / 2));
    
    return (
      <Card className={`mb-4 overflow-hidden transition-all duration-300 ${
        isEnabled ? 'border-primary/30 shadow-md bg-gradient-to-r from-card to-background/80' : 'hover:border-primary/20 hover:shadow-sm'
      }`} key={systemKey}>
        <CardContent className="p-0">
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-full ${isEnabled ? 'bg-primary/10 text-primary' : 'text-muted-foreground'}`}>
                {icon}
              </div>
              <h3 className="text-lg font-medium">{combinedLabel}</h3>
            </div>
            <div className="inline-flex h-7 w-14 cursor-pointer items-center rounded-full bg-slate-100 p-1 shadow-inner dark:bg-slate-800 transition-all duration-300">
              <div 
                className={`flex h-6 w-6 items-center justify-center rounded-full transition-transform duration-300 ${
                  isEnabled ? 'translate-x-7 bg-blue-500 text-white' : 'bg-slate-300 dark:bg-slate-600'
                }`}
                onClick={() => {
                  updateSystemCategory(systemKey as keyof typeof systemCategories, !isEnabled);
                  if (isAudioVideo) {
                    // If toggling audio, also toggle video
                    updateSystemCategory('video' as keyof typeof systemCategories, !isEnabled);
                  }
                }}
              >
                {isEnabled && <span className="flex h-2 w-2 rounded-full bg-white"></span>}
              </div>
            </div>
          </div>
          
          {isEnabled && (
            <div className="p-4 grid grid-cols-2 gap-x-8 gap-y-3">
              <div className="space-y-3">
                {leftColumnItems.map(([subKey, subData]) => (
                  <div className="flex items-center space-x-2" key={subKey}>
                    <Checkbox 
                      id={`${systemKey}-${subKey}`}
                      checked={subData.enabled}
                      onCheckedChange={(checked) => {
                        updateSubcategory(
                          systemKey, 
                          subKey, 
                          checked === 'indeterminate' ? false : Boolean(checked)
                        );
                      }}
                    />
                    <label 
                      htmlFor={`${systemKey}-${subKey}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {subData.name || subKey
                        .replace(/([A-Z])/g, ' $1')
                        .replace(/^./, (str) => str.toUpperCase())}
                    </label>
                  </div>
                ))}
              </div>
              <div className="space-y-3">
                {rightColumnItems.map(([subKey, subData]) => (
                  <div className="flex items-center space-x-2" key={subKey}>
                    <Checkbox 
                      id={`${systemKey}-${subKey}`}
                      checked={subData.enabled}
                      onCheckedChange={(checked) => {
                        updateSubcategory(
                          systemKey, 
                          subKey, 
                          checked === 'indeterminate' ? false : Boolean(checked)
                        );
                      }}
                    />
                    <label 
                      htmlFor={`${systemKey}-${subKey}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {subData.name || subKey
                        .replace(/([A-Z])/g, ' $1')
                        .replace(/^./, (str) => str.toUpperCase())}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="animate-fade-in">
      <h2 className="text-2xl font-bold mb-4">Home Automation Systems</h2>
      <p className="text-muted-foreground mb-6">
        Select the systems you want to include in your client's project. You can customize each system further in the next steps.
      </p>

      {defaultPackageCount > 0 && (
        <Alert className="mb-6 bg-amber-50 border-amber-200">
          <div className="flex items-center gap-2">
            <CheckCircle2 className="h-4 w-4 text-amber-500" />
            <div className="font-medium">Default Products Available</div>
          </div>
          <AlertDescription className="mt-2">
            {defaultPackageCount} default product{defaultPackageCount !== 1 ? 's' : ''} will automatically 
            be applied to your walkthrough based on the systems you select.
          </AlertDescription>
        </Alert>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
        <TabsList className="grid grid-cols-2 w-full max-w-md mx-auto mb-6 rounded-lg overflow-hidden bg-gradient-to-r from-background to-muted/30">
          <TabsTrigger value="systems" className="font-medium data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-secondary/80 data-[state=active]:text-primary-foreground">Systems</TabsTrigger>
          <TabsTrigger value="packages" className="flex items-center font-medium data-[state=active]:bg-gradient-to-r data-[state=active]:from-primary/80 data-[state=active]:to-secondary/80 data-[state=active]:text-primary-foreground">
            <Package className="h-4 w-4 mr-2" />
            Products
            {defaultPackageCount > 0 && (
              <Badge variant="secondary" className="ml-2">{defaultPackageCount}</Badge>
            )}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="systems" className="space-y-6">
          {Object.entries(systemSubcategories).map(([systemKey, subcategories]) => 
            renderSystemCard(systemKey, subcategories)
          )}
        </TabsContent>

        <TabsContent value="packages">
          <WeQuotePackageManager hideHeader={true} viewMode="card" />
        </TabsContent>
      </Tabs>

      <FormNavigation />
    </div>
  );
};

export default SystemSelectionStep;
