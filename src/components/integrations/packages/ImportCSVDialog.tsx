
import React, { useState, useRef, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { FileUp, Upload, Download, Check, ArrowRight } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useToast } from '@/hooks/use-toast';

export interface ImportCSVDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onImport: (csvContent: string, fieldMapping?: Record<string, string>) => void;
}

// Define available database fields for mapping
const DATABASE_FIELDS = [
  { key: 'name', label: 'Product Name', required: true },
  { key: 'description', label: 'Description', required: false },
  { key: 'system_category', label: 'System Category', required: true },
  { key: 'unit_cost', label: 'Unit Cost', required: true },
  { key: 'quantity', label: 'Quantity', required: false },
  { key: 'manufacturer', label: 'Manufacturer', required: false },
  { key: 'model', label: 'Model', required: false },
  { key: 'sku_number', label: 'SKU Number', required: false },
  { key: 'unit', label: 'Unit', required: false },
  { key: 'manufacturer_sku', label: 'Manufacturer SKU', required: false },
  { key: 'image_url', label: 'Image URL', required: false },
  { key: 'msrp', label: 'MSRP', required: false },
  { key: 'trade_price', label: 'Trade Price', required: false },
  { key: 'document_url', label: 'Document URL', required: false },
  { key: 'document_name', label: 'Document Name', required: false },
  { key: 'discontinued', label: 'Discontinued', required: false },
];

const ImportCSVDialog: React.FC<ImportCSVDialogProps> = ({
  open,
  onOpenChange,
  onImport,
}) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [csvPreview, setCsvPreview] = useState<string>('');
  const [csvHeaders, setCsvHeaders] = useState<string[]>([]);
  const [csvRows, setCsvRows] = useState<string[][]>([]);
  const [importSuccess, setImportSuccess] = useState(false);
  const [activeTab, setActiveTab] = useState<string>("preview");
  const [fieldMapping, setFieldMapping] = useState<Record<string, string>>({});
  const [mappingValid, setMappingValid] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  
  // Validate field mapping whenever it changes
  useEffect(() => {
    if (csvHeaders.length === 0) {
      setMappingValid(false);
      return;
    }

    // Check if required fields are mapped
    const requiredFields = DATABASE_FIELDS.filter(field => field.required);
    const isMappingValid = requiredFields.every(field => 
      Object.values(fieldMapping).includes(field.key)
    );
    
    setMappingValid(isMappingValid);
  }, [fieldMapping, csvHeaders]);
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      setSelectedFile(file);
      
      // Generate preview
      const reader = new FileReader();
      reader.onload = (event) => {
        const content = event.target?.result as string;
        // Extract headers and rows
        const lines = content.split('\n');
        if (lines.length > 0) {
          // Parse CSV properly, handling quoted values
          const parseCSVLine = (line: string): string[] => {
            const result: string[] = [];
            let inQuote = false;
            let currentValue = '';
            
            for (let i = 0; i < line.length; i++) {
              const char = line[i];
              
              if (char === '"' && (i === 0 || line[i-1] !== '\\')) {
                inQuote = !inQuote;
              } else if (char === ',' && !inQuote) {
                result.push(currentValue.trim());
                currentValue = '';
              } else {
                currentValue += char;
              }
            }
            
            // Add the last value
            if (currentValue) {
              result.push(currentValue.trim());
            }
            
            return result;
          };
          
          const headers = parseCSVLine(lines[0]);
          setCsvHeaders(headers);
          
          // Initialize field mapping with best-guess defaults
          const initialMapping: Record<string, string> = {};
          headers.forEach(header => {
            // Try to match headers to database fields
            const normalizedHeader = header.toLowerCase().trim();
            const matchedField = DATABASE_FIELDS.find(field => 
              normalizedHeader === field.key || 
              normalizedHeader === field.label.toLowerCase() ||
              normalizedHeader.includes(field.key)
            );
            
            if (matchedField) {
              initialMapping[header] = matchedField.key;
            }
          });
          setFieldMapping(initialMapping);
          
          // Get rows for preview (limited to 5)
          const previewRows: string[][] = [];
          const maxPreviewRows = Math.min(5, lines.length - 1);
          for (let i = 1; i <= maxPreviewRows; i++) {
            if (lines[i].trim()) {
              previewRows.push(parseCSVLine(lines[i]));
            }
          }
          setCsvRows(previewRows);
          
          // Keep original CSV content for reference
          setCsvPreview(content);

          // Show mapping tab after successful file load
          setActiveTab("mapping");
        }
      };
      reader.readAsText(file);
    }
  };
  
  const handleImport = async () => {
    if (!selectedFile) return;
    
    setIsUploading(true);
    setImportSuccess(false);
    
    try {
      // Read file content
      const content = await selectedFile.text();
      await onImport(content, fieldMapping);
      setImportSuccess(true);
      
      // Wait a moment before resetting the form to allow the user to see the success state
      setTimeout(() => {
        resetFormState();
        onOpenChange(false);
      }, 1500);
    } catch (error) {
      console.error('Error reading CSV file:', error);
      toast({
        title: 'Import Failed',
        description: 'There was an error processing your CSV file',
        variant: 'destructive'
      });
    } finally {
      setIsUploading(false);
    }
  };
  
  const resetFormState = () => {
    setSelectedFile(null);
    setCsvPreview('');
    setCsvHeaders([]);
    setCsvRows([]);
    setFieldMapping({});
    setActiveTab("preview");
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const downloadTemplateCSV = () => {
    const headers = [
      "SKU", "Description", "Long Description", "Model", 
      "Category Group", "Category", "Manufacturer", "Manufacturer SKU", 
      "Image URL", "Document Name", "Document URL", "Unit Of Measure", 
      "Buy Cost USD", "Trade Price USD", "MSRP USD", "EAN", "Discontinued"
    ];
    
    const sampleData = [
      ["SKU123", "LED Light Fixture", "Dimmable LED fixture with warm white light", "X100", 
       "Residential", "lighting", "Acme Lighting", "MFR-123", 
       "https://example.com/light.jpg", "Spec Sheet", "https://example.com/spec.pdf", "piece", 
       "99.99", "149.99", "199.99", "1234567890123", "false"],
      ["SKU456", "Motorized Shade", "Smart motorized shade with remote control", "SH200", 
       "Commercial", "shades", "Shade Co", "SH-456", 
       "https://example.com/shade.jpg", "User Guide", "https://example.com/guide.pdf", "each", 
       "299.99", "399.99", "499.99", "2345678901234", "false"]
    ];
    
    const csvContent = [
      headers.join(','),
      ...sampleData.map(row => row.join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.download = 'product_catalog_template.csv';
    link.href = url;
    link.click();
    URL.revokeObjectURL(url);
  };
  
  const handleFieldMappingChange = (csvHeader: string, dbField: string) => {
    setFieldMapping(prev => ({
      ...prev,
      [csvHeader]: dbField
    }));
  };
  
  const renderMappingTab = () => {
    if (csvHeaders.length === 0) {
      return (
        <div className="text-center py-6">
          <p>Please upload a CSV file first to map fields.</p>
        </div>
      );
    }

    return (
      <div className="space-y-4">
        <Alert className="bg-blue-50 border-blue-200">
          <AlertDescription>
            Map CSV columns to database fields. Required fields are marked with <span className="text-destructive">*</span>
          </AlertDescription>
        </Alert>
        
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>CSV Column</TableHead>
              <TableHead></TableHead>
              <TableHead>Database Field</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {csvHeaders.map((header) => (
              <TableRow key={header}>
                <TableCell>{header}</TableCell>
                <TableCell className="text-center">
                  <ArrowRight className="h-4 w-4 mx-auto text-muted-foreground" />
                </TableCell>
                <TableCell>
                  <Select
                    value={fieldMapping[header] || ''}
                    onValueChange={(value) => handleFieldMappingChange(header, value)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Select field" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">-- Ignore this column --</SelectItem>
                      {DATABASE_FIELDS.map((field) => (
                        <SelectItem key={field.key} value={field.key}>
                          {field.label} {field.required && <span className="text-destructive">*</span>}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        {!mappingValid && (
          <Alert variant="destructive">
            <AlertDescription>
              Please map all required fields (marked with *) before importing
            </AlertDescription>
          </Alert>
        )}
      </div>
    );
  };
  
  const renderPreviewTab = () => {
    return (
      <>
        {csvHeaders.length > 0 && (
          <div className="mt-4">
            <p className="text-sm font-medium mb-1">Detected Headers:</p>
            <div className="text-xs p-2 bg-muted/40 rounded border overflow-x-auto">
              <div className="flex flex-wrap gap-1.5">
                {csvHeaders.map((header, index) => (
                  <Badge key={index} className="bg-primary/10 text-primary">
                    {header}
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        )}
        
        {csvRows.length > 0 && (
          <div className="mt-4">
            <p className="text-sm font-medium mb-1">Preview:</p>
            <div className="rounded border overflow-auto max-h-[300px]">
              <Table>
                <TableHeader>
                  <TableRow>
                    {csvHeaders.map((header, index) => (
                      <TableHead key={index} className="whitespace-nowrap text-xs">
                        {header}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {csvRows.map((row, rowIndex) => (
                    <TableRow key={rowIndex}>
                      {row.map((cell, cellIndex) => (
                        <TableCell key={cellIndex} className="text-xs py-2">
                          {cell.length > 50 ? `${cell.substring(0, 50)}...` : cell}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        )}
      </>
    );
  };
  
  const renderHelpTab = () => {
    return (
      <div className="space-y-4 py-2">
        <Alert className="bg-blue-50 border-blue-200 text-sm">
          <AlertDescription>
            <p className="mb-1 font-medium">Your CSV file should include these headers:</p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
              <ul className="list-disc pl-5 space-y-0.5 text-xs">
                <li><strong>SKU</strong> - Product identifier</li>
                <li><strong>Description</strong> - Short product description</li>
                <li><strong>Long Description</strong> - Detailed description</li>
                <li><strong>Model</strong> - Product model number</li>
                <li><strong>Category Group</strong> - Product group (e.g., Residential)</li>
                <li><strong>Category</strong> - System category (lighting, audio, etc.)</li>
                <li><strong>Manufacturer</strong> - Product manufacturer</li>
                <li><strong>Manufacturer SKU</strong> - Manufacturer's part number</li>
              </ul>
              <ul className="list-disc pl-5 space-y-0.5 text-xs">
                <li><strong>Image URL</strong> - Product image link</li>
                <li><strong>Document Name</strong> - Name of related document</li>
                <li><strong>Document URL</strong> - Link to product document</li>
                <li><strong>Unit Of Measure</strong> - e.g., piece, each, set</li>
                <li><strong>Buy Cost USD</strong> - Cost price</li>
                <li><strong>Trade Price USD</strong> - Wholesale price</li>
                <li><strong>MSRP USD</strong> - Retail price</li>
                <li><strong>EAN</strong> - Barcode number</li>
                <li><strong>Discontinued</strong> - true/false</li>
              </ul>
            </div>
          </AlertDescription>
        </Alert>
        
        <div>
          <h4 className="font-medium mb-2">Import process:</h4>
          <ol className="list-decimal pl-5 space-y-1 text-sm">
            <li>Upload your CSV file with product data</li>
            <li>Map CSV columns to database fields in the mapping tab</li>
            <li>Review data preview to ensure correct formatting</li>
            <li>Click "Import" to add products to your catalog</li>
          </ol>
        </div>
        
        <div>
          <h4 className="font-medium mb-2">Required fields:</h4>
          <ul className="list-disc pl-5 space-y-1 text-sm">
            {DATABASE_FIELDS.filter(f => f.required).map(field => (
              <li key={field.key}><strong>{field.label}</strong></li>
            ))}
          </ul>
        </div>
      </div>
    );
  };
  
  return (
    <Dialog open={open} onOpenChange={(value) => {
      if (!value) {
        resetFormState();
      }
      onOpenChange(value);
    }}>
      <DialogContent className="sm:max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Import from CSV</DialogTitle>
          <DialogDescription>
            Upload a CSV file to import items. Map your CSV columns to database fields to ensure correct data import.
          </DialogDescription>
        </DialogHeader>
        
        {importSuccess ? (
          <div className="py-8 text-center">
            <div className="flex flex-col items-center justify-center mb-4">
              <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-2">
                <Check className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="text-lg font-medium">Import Successful</h3>
              <p className="text-muted-foreground mt-1">Your products have been imported successfully</p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <p className="text-sm font-medium">Need a template to get started?</p>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={downloadTemplateCSV}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                CSV Template
              </Button>
            </div>
            
            <div className="grid w-full max-w-sm items-center gap-1.5">
              <label
                htmlFor="csv-file"
                className="flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-lg cursor-pointer hover:bg-muted/50"
              >
                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                  <FileUp className="w-10 h-10 mb-3 text-muted-foreground" />
                  <p className="mb-2 text-sm text-muted-foreground">
                    <span className="font-semibold">Click to upload</span> or drag and drop
                  </p>
                  <p className="text-xs text-muted-foreground">CSV file only</p>
                </div>
                <input
                  id="csv-file"
                  type="file"
                  accept=".csv"
                  className="hidden"
                  onChange={handleFileChange}
                  ref={fileInputRef}
                />
              </label>
              
              {selectedFile && (
                <div className="text-sm text-muted-foreground">
                  Selected: {selectedFile.name}
                </div>
              )}
            </div>
            
            {csvHeaders.length > 0 && (
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="preview">Preview Data</TabsTrigger>
                  <TabsTrigger value="mapping">Map Fields</TabsTrigger>
                  <TabsTrigger value="help">Help</TabsTrigger>
                </TabsList>
                
                <TabsContent value="preview" className="pt-4">
                  {renderPreviewTab()}
                </TabsContent>
                
                <TabsContent value="mapping" className="pt-4">
                  {renderMappingTab()}
                </TabsContent>
                
                <TabsContent value="help" className="pt-4">
                  {renderHelpTab()}
                </TabsContent>
              </Tabs>
            )}
            
            <Separator />
          </div>
        )}
        
        {!importSuccess && (
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              resetFormState();
              onOpenChange(false);
            }}>
              Cancel
            </Button>
            <Button 
              onClick={handleImport} 
              disabled={!selectedFile || isUploading || (csvHeaders.length > 0 && !mappingValid)}
              className="flex items-center"
            >
              {isUploading ? 'Importing...' : (
                <>
                  <Upload className="h-4 w-4 mr-2" />
                  Import
                </>
              )}
            </Button>
          </DialogFooter>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default ImportCSVDialog;
