import React, { useState, useEffect } from 'react';
import usePackageManagement, { systemCategories, ProductItem } from '@/hooks/usePackageManagement'; 
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { LayoutGrid, LayoutList, Import, Upload, Plus } from 'lucide-react';
import { ToggleGroup, ToggleGroupItem } from '@/components/ui/toggle-group';
import { Badge } from '@/components/ui/badge';
import ItemsByCategory from './products/ItemsByCategory';
import ProductSubcategoryMapping from '../products/ProductSubcategoryMapping';
import ImportTemplateDialog from './packages/ImportTemplateDialog';
import ImportCSVDialog from './packages/ImportCSVDialog';
import { toast } from "sonner";

interface ProductCatalogManagerProps {
  isStandalone?: boolean;
  hideHeader?: boolean;
  bulkEditMode?: boolean;
  selectedItems?: string[];
  onSelectItem?: (itemId: string, isSelected: boolean) => void;
}

const ProductCatalogManager: React.FC<ProductCatalogManagerProps> = ({
  isStandalone = false,
  hideHeader = false,
  bulkEditMode = false,
  selectedItems = [],
  onSelectItem
}) => {
  const [viewMode, setViewMode] = useState<'card' | 'list'>('card');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedSubcategory, setSelectedSubcategory] = useState<string | null>(null);
  const [isMappingDialogOpen, setIsMappingDialogOpen] = useState(false);

  const {
    items,
    currentItem,
    isLoading,
    isAddDialogOpen,
    isEditDialogOpen,
    isImportDialogOpen,
    isImportCSVDialogOpen,
    templateUrl,
    setTemplateUrl,
    handleAddItem,
    handleEditItem,
    handleDeleteItem,
    handleUpdateItem,
    updateItemSubcategories,
    getItemsByCategory,
    getAllItems,
    getItemsBySubcategory,
    handleImportTemplate,
    handleImportCSV,
    handleImportCSVContent,
    setIsAddDialogOpen,
    setIsEditDialogOpen,
    setIsImportDialogOpen,
    setIsImportCSVDialogOpen,
    handleSaveNewItem,
    handleSaveExistingItem
  } = usePackageManagement({ isStandalone });

  // Get all categories for filtering
  const getAllCategories = () => {
    if (selectedCategory === 'all') {
      return Object.keys(systemCategories);
    } else {
      return [selectedCategory];
    }
  };

  // Filter items based on selected category and subcategory
  const getFilteredItems = () => {
    if (selectedCategory === 'all') {
      if (selectedSubcategory) {
        // If subcategory is selected with "all", filter by that specific subcategory
        return getItemsBySubcategory(selectedSubcategory);
      }
      return getAllItems();
    } else if (selectedSubcategory) {
      // Filter by specific subcategory
      return getItemsByCategory(selectedCategory, selectedSubcategory);
    } else {
      // Filter by category only
      return getItemsByCategory(selectedCategory);
    }
  };

  // Handle subcategory selection
  const handleSubcategoryChange = (subcategories: string[]) => {
    if (currentItem) {
      updateItemSubcategories(subcategories);
    }
  };

  // Handle item form submission
  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentItem) return;
    
    if (isAddDialogOpen) {
      handleSaveNewItem(currentItem);
      toast(`${currentItem.name} has been added to the catalog.`);
    } else if (isEditDialogOpen) {
      handleSaveExistingItem(currentItem);
      toast(`${currentItem.name} has been updated successfully.`);
    }
  };

  // This function handles the edit/delete actions and converts string IDs to ProductItem objects when needed
  const handleItemAction = (action: 'edit' | 'delete', itemId: string | ProductItem) => {
    // If we received an ID (string), find the item first
    if (typeof itemId === 'string') {
      const item = items.find(item => item.id === itemId);
      if (!item) {
        console.error(`Item with ID ${itemId} not found`);
        return;
      }
      
      // Call the appropriate handler with the found item
      if (action === 'edit') {
        handleEditItem(item);
      } else if (action === 'delete') {
        handleDeleteItem(item);
      }
    } else {
      // We already have the item object
      if (action === 'edit') {
        handleEditItem(itemId);
      } else if (action === 'delete') {
        handleDeleteItem(itemId);
      }
    }
  };

  return (
    <div className="container max-w-7xl mx-auto">
      {!hideHeader && (
        <div className="flex items-center justify-between py-4">
          <h1 className="text-2xl font-bold">Product Catalog</h1>
          <div className="flex gap-2">
            <ToggleGroup type="single" value={viewMode} onValueChange={(value) => value && setViewMode(value as 'card' | 'list')}>
              <ToggleGroupItem value="card" aria-label="Card View">
                <LayoutGrid className="h-4 w-4" />
              </ToggleGroupItem>
              <ToggleGroupItem value="list" aria-label="List View">
                <LayoutList className="h-4 w-4" />
              </ToggleGroupItem>
            </ToggleGroup>
            <Button onClick={() => setIsMappingDialogOpen(true)}>
              Manage Subcategories
            </Button>
            <Button onClick={handleAddItem}>
              <Plus className="mr-2 h-4 w-4" />
              Add Product
            </Button>
          </div>
        </div>
      )}

      {/* Import options */}
      <div className="flex flex-wrap gap-2 mb-4">
        <Button variant="outline" onClick={() => setIsImportDialogOpen(true)}>
          <Import className="mr-2 h-4 w-4" />
          Import Template
        </Button>
        <Button variant="outline" onClick={() => setIsImportCSVDialogOpen(true)}>
          <Upload className="mr-2 h-4 w-4" />
          Import CSV
        </Button>
      </div>

      {/* Category and Subcategory Filters */}
      <div className="flex items-center space-x-4 py-2">
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Systems</SelectItem>
            {Object.entries(systemCategories).map(([key, label]) => (
              <SelectItem key={key} value={key}>{label}</SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Subcategory Dialog */}
        <Dialog open={isMappingDialogOpen} onOpenChange={setIsMappingDialogOpen}>
          <DialogContent className="sm:max-w-[600px]">
            <DialogHeader>
              <DialogTitle>Manage Product Subcategories</DialogTitle>
              <DialogDescription>
                Map products to specific subcategories to improve organization and selection options
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label>System Category</Label>
                <Select 
                  value={currentItem?.systemCategory || 'lighting'}
                  onValueChange={(value) => {
                    if (currentItem) {
                      handleUpdateItem('systemCategory', value);
                    }
                  }}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select system category" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(systemCategories).map(([key, label]) => (
                      <SelectItem key={key} value={key}>{label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              {currentItem && (
                <ProductSubcategoryMapping 
                  systemCategory={currentItem.systemCategory || 'lighting'}
                  selectedSubcategories={currentItem.subcategories || []}
                  onChange={handleSubcategoryChange}
                />
              )}
              
              <div className="flex justify-end">
                <Button onClick={() => setIsMappingDialogOpen(false)}>
                  Done
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Products display */}
      <ItemsByCategory
        categories={getAllCategories()}
        items={getFilteredItems()}
        onAddItem={() => handleAddItem()}
        onEditItem={(id) => handleItemAction('edit', id)}
        onDeleteItem={(id) => handleItemAction('delete', id)}
        isStandalone={isStandalone}
        viewMode={viewMode}
        bulkEditMode={bulkEditMode}
        selectedItems={selectedItems}
        onSelectItem={onSelectItem}
      />
      
      {/* Import Template Dialog */}
      <ImportTemplateDialog
        open={isImportDialogOpen}
        onOpenChange={setIsImportDialogOpen}
        templateUrl={templateUrl}
        onTemplateUrlChange={setTemplateUrl}
        onImport={() => handleImportTemplate()}
      />
      
      {/* Import CSV Dialog */}
      <ImportCSVDialog
        open={isImportCSVDialogOpen}
        onOpenChange={setIsImportCSVDialogOpen}
        onImport={(csvContent) => handleImportCSVContent(csvContent)}
      />

      {/* Add/Edit Product Dialog */}
      <Dialog open={isAddDialogOpen || isEditDialogOpen} onOpenChange={(open) => {
        if (!open) {
          setIsAddDialogOpen(false);
          setIsEditDialogOpen(false);
        }
      }}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{isAddDialogOpen ? 'Add New Product' : 'Edit Product'}</DialogTitle>
            <DialogDescription>
              {isAddDialogOpen 
                ? 'Add a new product to your catalog' 
                : 'Update the product details in your catalog'}
            </DialogDescription>
          </DialogHeader>
          
          <form onSubmit={handleFormSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Product Name</Label>
              <Input 
                id="name" 
                value={currentItem?.name || ''} 
                onChange={(e) => handleUpdateItem('name', e.target.value)}
                placeholder="Enter product name"
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea 
                id="description" 
                value={currentItem?.description || ''} 
                onChange={(e) => handleUpdateItem('description', e.target.value)}
                placeholder="Enter product description"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="systemCategory">System Category</Label>
                <Select 
                  value={currentItem?.systemCategory || 'lighting'} 
                  onValueChange={(value) => handleUpdateItem('systemCategory', value)}
                >
                  <SelectTrigger id="systemCategory">
                    <SelectValue placeholder="Select system" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(systemCategories).map(([key, label]) => (
                      <SelectItem key={key} value={key}>{label}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="manufacturer">Manufacturer</Label>
                <Input 
                  id="manufacturer" 
                  value={currentItem?.manufacturer || ''} 
                  onChange={(e) => handleUpdateItem('manufacturer', e.target.value)}
                  placeholder="Manufacturer name"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="model">Model Number</Label>
                <Input 
                  id="model" 
                  value={currentItem?.model || ''} 
                  onChange={(e) => handleUpdateItem('model', e.target.value)}
                  placeholder="Model number"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="sku">SKU</Label>
                <Input 
                  id="sku" 
                  value={currentItem?.sku || ''} 
                  onChange={(e) => handleUpdateItem('sku', e.target.value)}
                  placeholder="Stock keeping unit"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="unitCost">Unit Cost</Label>
                <Input 
                  id="unitCost" 
                  type="number" 
                  min="0"
                  step="0.01"
                  value={currentItem?.unitCost || 0} 
                  onChange={(e) => handleUpdateItem('unitCost', parseFloat(e.target.value))}
                  placeholder="0.00"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="msrp">MSRP</Label>
                <Input 
                  id="msrp" 
                  type="number" 
                  min="0"
                  step="0.01"
                  value={currentItem?.msrp || 0} 
                  onChange={(e) => handleUpdateItem('msrp', parseFloat(e.target.value))}
                  placeholder="0.00"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="tradePrice">Trade Price</Label>
                <Input 
                  id="tradePrice" 
                  type="number" 
                  min="0"
                  step="0.01"
                  value={currentItem?.tradePrice || 0} 
                  onChange={(e) => handleUpdateItem('tradePrice', parseFloat(e.target.value))}
                  placeholder="0.00"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="quantity">Quantity</Label>
                <Input 
                  id="quantity" 
                  type="number" 
                  min="1"
                  value={currentItem?.quantity || 1} 
                  onChange={(e) => handleUpdateItem('quantity', parseInt(e.target.value))}
                  placeholder="1"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="unit">Unit</Label>
                <Input 
                  id="unit" 
                  value={currentItem?.unit || 'piece'} 
                  onChange={(e) => handleUpdateItem('unit', e.target.value)}
                  placeholder="piece"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="imageUrl">Image URL</Label>
              <Input 
                id="imageUrl" 
                value={currentItem?.imageUrl || ''} 
                onChange={(e) => handleUpdateItem('imageUrl', e.target.value)}
                placeholder="https://example.com/image.jpg"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="documentUrl">Document URL</Label>
              <Input 
                id="documentUrl" 
                value={currentItem?.documentUrl || ''} 
                onChange={(e) => handleUpdateItem('documentUrl', e.target.value)}
                placeholder="https://example.com/document.pdf"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="documentName">Document Name</Label>
              <Input 
                id="documentName" 
                value={currentItem?.documentName || ''} 
                onChange={(e) => handleUpdateItem('documentName', e.target.value)}
                placeholder="Product Manual"
              />
            </div>
            
            <div className="flex justify-end gap-2 pt-4">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => {
                  setIsAddDialogOpen(false);
                  setIsEditDialogOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button type="submit">
                {isAddDialogOpen ? 'Add Product' : 'Save Changes'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default ProductCatalogManager;
