
import React from 'react';
import { useFormContext } from '../context/FormContext';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { useIsMobile } from '@/hooks/use-mobile';
import { Check } from 'lucide-react';

const FormProgress: React.FC = () => {
  const { formData, getTotalSteps, goToStep } = useFormContext();
  const { currentStep } = formData;
  const totalSteps = getTotalSteps();
  
  // Adjust the progress calculation to be based on the current step (1-based index)
  // and the total number of steps
  const progress = ((currentStep || 1) / totalSteps) * 100;
  const isMobile = useIsMobile();

  // These steps should match the exact indexes used in the renderStep function
  const steps = [
    "Client Info",     // step 1
    "Qualification",   // step 2
    "Systems",         // step 3
    "Rooms",           // step 4
    "Files",           // step 5
    "Summary",         // step 6
    "Actions"          // step 7
  ];

  // Only show steps up to the total configured steps
  const visibleSteps = steps.slice(0, totalSteps);

  const handleStepClick = (index: number) => {
    // Adjust the index to match the 1-based indexing in the form context
    const stepIndex = index + 1;
    
    // Only allow going to previous steps or current step
    if (stepIndex <= (currentStep || 1)) {
      goToStep(stepIndex);
    }
  };

  return (
    <div className="w-full mb-4 md:mb-6">
      <div className="flex justify-between mb-2 overflow-x-auto hide-scrollbar py-2">
        {visibleSteps.map((step, index) => {
          // Adjust the index to match the 1-based indexing in the form context
          const stepIndex = index + 1;
          const isCompleted = stepIndex < (currentStep || 1);
          const isCurrent = stepIndex === (currentStep || 1);
          const isClickable = stepIndex <= (currentStep || 1);
          
          return (
            <div key={index} className="flex items-center">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div 
                      className={`flex flex-col items-center ${
                        isClickable ? "cursor-pointer touch-manipulation" : "cursor-not-allowed"
                      }`}
                      onClick={() => handleStepClick(index)}
                      role="button"
                      tabIndex={isClickable ? 0 : -1}
                      aria-label={`Go to ${step} step`}
                    >
                      <div
                        className={`step-indicator flex items-center justify-center rounded-full transition-all ${
                          isCompleted
                            ? "bg-[#37372f] text-white"
                            : isCurrent
                            ? "bg-[#4a4a3f] text-white border-2 border-[#37372f]"
                            : "bg-gray-200 text-gray-500 border border-gray-300"
                        }`}
                      >
                        {isCompleted ? (
                          <Check className="h-3 w-3 md:h-4 md:w-4" />
                        ) : (
                          <span className="text-xs md:text-sm font-medium">
                            {stepIndex}
                          </span>
                        )}
                      </div>
                      {!isMobile && (
                        <span className={`text-xs mt-1 whitespace-nowrap ${
                          isCurrent ? "font-medium text-[#37372f]" : 
                          isCompleted ? "text-[#37372f]" : "text-gray-500"
                        }`}>
                          {step}
                        </span>
                      )}
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    {step}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
              {index < visibleSteps.length - 1 && (
                <div
                  className={`step-connector my-auto ${
                    stepIndex < (currentStep || 1) ? "bg-[#37372f]" : "bg-gray-300"
                  }`}
                  aria-hidden="true"
                ></div>
              )}
            </div>
          );
        })}
      </div>
      <div className="progress-bar rounded-full overflow-hidden bg-gray-200">
        <div
          className="progress-bar-fill bg-[#37372f] h-1.5 rounded-full transition-all duration-300 ease-in-out"
          style={{ width: `${progress}%` }}
          role="progressbar"
          aria-valuenow={progress}
          aria-valuemin={0}
          aria-valuemax={100}
        ></div>
      </div>
    </div>
  );
};

export default FormProgress;
