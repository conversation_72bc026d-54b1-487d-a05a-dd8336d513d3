import { supabase } from '@/integrations/supabase/client';

export const runSubcategoryMigration = async () => {
  try {
    console.log('Running subcategory migration...');

    // Check if table already exists
    const { data: tables, error: tableError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'product_subcategories');

    if (tableError) {
      console.error('Error checking for existing table:', tableError);
      return false;
    }

    if (tables && tables.length > 0) {
      console.log('product_subcategories table already exists');
      return true;
    }

    // Create the table using raw SQL
    const createTableSQL = `
      -- Create product_subcategories table for custom subcategory management
      CREATE TABLE IF NOT EXISTS public.product_subcategories (
        id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
        name TEXT NOT NULL,
        system_category TEXT NOT NULL,
        order_index INTEGER DEFAULT 0,
        description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
      );

      -- Create indexes for better query performance
      CREATE INDEX IF NOT EXISTS idx_product_subcategories_system_category ON public.product_subcategories(system_category);
      CREATE INDEX IF NOT EXISTS idx_product_subcategories_order ON public.product_subcategories(system_category, order_index);

      -- Create unique constraint to prevent duplicate subcategory names within the same system category
      CREATE UNIQUE INDEX IF NOT EXISTS idx_product_subcategories_unique_name 
      ON public.product_subcategories(system_category, LOWER(name));

      -- Add RLS (Row Level Security) policies if needed
      ALTER TABLE public.product_subcategories ENABLE ROW LEVEL SECURITY;

      -- Create policy to allow all operations for authenticated users
      CREATE POLICY IF NOT EXISTS "Allow all operations for authenticated users" ON public.product_subcategories
      FOR ALL USING (true);
    `;

    const { error: createError } = await supabase.rpc('exec_sql', { sql: createTableSQL });

    if (createError) {
      console.error('Error creating subcategories table:', createError);
      return false;
    }

    console.log('Subcategory migration completed successfully');
    return true;
  } catch (error) {
    console.error('Error running subcategory migration:', error);
    return false;
  }
};

// Auto-run migration when this module is imported
runSubcategoryMigration();
