import { supabase } from '@/integrations/supabase/client';

export const setupSubcategoriesDatabase = async (): Promise<boolean> => {
  try {
    console.log('Setting up subcategories database...');

    // First, try to create the table using a simple approach
    const { error: createError } = await supabase.rpc('exec_sql', {
      sql: `
        -- Create product_subcategories table for custom subcategory management
        CREATE TABLE IF NOT EXISTS public.product_subcategories (
          id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
          name TEXT NOT NULL,
          system_category TEXT NOT NULL,
          order_index INTEGER DEFAULT 0,
          description TEXT,
          is_default BOOLEAN DEFAULT false,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
        );

        -- Create indexes for better query performance
        CREATE INDEX IF NOT EXISTS idx_product_subcategories_system_category ON public.product_subcategories(system_category);
        CREATE INDEX IF NOT EXISTS idx_product_subcategories_order ON public.product_subcategories(system_category, order_index);

        -- Create unique constraint to prevent duplicate subcategory names within the same system category
        CREATE UNIQUE INDEX IF NOT EXISTS idx_product_subcategories_unique_name 
        ON public.product_subcategories(system_category, LOWER(name));

        -- Add RLS (Row Level Security) policies if needed
        ALTER TABLE public.product_subcategories ENABLE ROW LEVEL SECURITY;

        -- Create policy to allow all operations for authenticated users
        CREATE POLICY IF NOT EXISTS "Allow all operations for authenticated users" ON public.product_subcategories
        FOR ALL USING (true);
      `
    });

    if (createError) {
      console.error('Error creating table with RPC:', createError);
      // Try alternative approach - direct table creation
      const { error: directError } = await supabase
        .from('product_subcategories')
        .select('id')
        .limit(1);

      if (directError && directError.message.includes('does not exist')) {
        console.log('Table does not exist, attempting manual creation...');
        // Table doesn't exist, we'll need to create it manually
        return false;
      }
    }

    // Check if default subcategories already exist
    const { data: existingDefaults, error: checkError } = await supabase
      .from('product_subcategories')
      .select('id')
      .eq('is_default', true)
      .limit(1);

    if (checkError) {
      console.error('Error checking for existing defaults:', checkError);
      return false;
    }

    if (existingDefaults && existingDefaults.length > 0) {
      console.log('Default subcategories already exist');
      return true;
    }

    // Insert default subcategories
    const defaultSubcategories = [
      // Lighting Subcategories
      { name: 'Recessed', system_category: 'lighting', order_index: 1, description: 'Recessed ceiling lights and downlights', is_default: true },
      { name: 'Pendant', system_category: 'lighting', order_index: 2, description: 'Hanging pendant lights and chandeliers', is_default: true },
      { name: 'Track', system_category: 'lighting', order_index: 3, description: 'Track lighting systems and fixtures', is_default: true },
      { name: 'Smart', system_category: 'lighting', order_index: 4, description: 'Smart and connected lighting solutions', is_default: true },
      { name: 'Dimmable', system_category: 'lighting', order_index: 5, description: 'Dimmable lighting fixtures and controls', is_default: true },
      { name: 'LED', system_category: 'lighting', order_index: 6, description: 'LED lighting fixtures and bulbs', is_default: true },
      { name: 'Outdoor', system_category: 'lighting', order_index: 7, description: 'Exterior and landscape lighting', is_default: true },
      { name: 'Under Cabinet', system_category: 'lighting', order_index: 8, description: 'Under cabinet and task lighting', is_default: true },
      { name: 'Decorative', system_category: 'lighting', order_index: 9, description: 'Decorative and accent lighting', is_default: true },

      // Audio Subcategories
      { name: 'Speakers', system_category: 'audio', order_index: 1, description: 'All types of speakers and drivers', is_default: true },
      { name: 'Amplifiers', system_category: 'audio', order_index: 2, description: 'Audio amplifiers and power systems', is_default: true },
      { name: 'Receivers', system_category: 'audio', order_index: 3, description: 'Audio/video receivers and processors', is_default: true },
      { name: 'Wireless', system_category: 'audio', order_index: 4, description: 'Wireless audio systems and components', is_default: true },
      { name: 'Ceiling', system_category: 'audio', order_index: 5, description: 'In-ceiling and overhead speakers', is_default: true },
      { name: 'In-Wall', system_category: 'audio', order_index: 6, description: 'In-wall speakers and components', is_default: true },
      { name: 'Outdoor', system_category: 'audio', order_index: 7, description: 'Weather-resistant outdoor audio', is_default: true },
      { name: 'Subwoofers', system_category: 'audio', order_index: 8, description: 'Subwoofers and bass management', is_default: true },
      { name: 'Soundbars', system_category: 'audio', order_index: 9, description: 'Soundbars and all-in-one systems', is_default: true },

      // Video Subcategories
      { name: 'Displays', system_category: 'video', order_index: 1, description: 'TVs, monitors, and display panels', is_default: true },
      { name: 'Projectors', system_category: 'video', order_index: 2, description: 'Video projectors and projection systems', is_default: true },
      { name: '4K/8K', system_category: 'video', order_index: 3, description: 'Ultra-high definition video equipment', is_default: true },
      { name: 'Streaming', system_category: 'video', order_index: 4, description: 'Streaming devices and media players', is_default: true },
      { name: 'Cables', system_category: 'video', order_index: 5, description: 'Video cables and connectivity', is_default: true },
      { name: 'Mounts', system_category: 'video', order_index: 6, description: 'TV and display mounting solutions', is_default: true },
      { name: 'Switchers', system_category: 'video', order_index: 7, description: 'Video switchers and matrix systems', is_default: true },
      { name: 'Extenders', system_category: 'video', order_index: 8, description: 'Video signal extenders and distribution', is_default: true },
      { name: 'Commercial', system_category: 'video', order_index: 9, description: 'Commercial-grade video equipment', is_default: true },

      // Security Subcategories
      { name: 'Cameras', system_category: 'security', order_index: 1, description: 'Security cameras and surveillance', is_default: true },
      { name: 'Access Control', system_category: 'security', order_index: 2, description: 'Door locks and access systems', is_default: true },
      { name: 'Alarms', system_category: 'security', order_index: 3, description: 'Alarm systems and sensors', is_default: true },
      { name: 'Intercoms', system_category: 'security', order_index: 4, description: 'Intercom and communication systems', is_default: true },
      { name: 'Motion Sensors', system_category: 'security', order_index: 5, description: 'Motion detection and sensors', is_default: true },
      { name: 'Smart Locks', system_category: 'security', order_index: 6, description: 'Smart and electronic door locks', is_default: true },
      { name: 'Outdoor', system_category: 'security', order_index: 7, description: 'Exterior security equipment', is_default: true },
      { name: 'Indoor', system_category: 'security', order_index: 8, description: 'Interior security systems', is_default: true },
      { name: 'Monitoring', system_category: 'security', order_index: 9, description: 'Security monitoring and recording', is_default: true },

      // Network Subcategories
      { name: 'Routers', system_category: 'network', order_index: 1, description: 'Network routers and gateways', is_default: true },
      { name: 'Switches', system_category: 'network', order_index: 2, description: 'Network switches and hubs', is_default: true },
      { name: 'Access Points', system_category: 'network', order_index: 3, description: 'Wireless access points and WiFi', is_default: true },
      { name: 'Cables', system_category: 'network', order_index: 4, description: 'Network cables and connectivity', is_default: true },
      { name: 'Managed', system_category: 'network', order_index: 5, description: 'Managed network equipment', is_default: true },
      { name: 'Unmanaged', system_category: 'network', order_index: 6, description: 'Unmanaged network equipment', is_default: true },
      { name: 'PoE', system_category: 'network', order_index: 7, description: 'Power over Ethernet equipment', is_default: true },
      { name: 'Fiber', system_category: 'network', order_index: 8, description: 'Fiber optic network components', is_default: true },
      { name: 'Enterprise', system_category: 'network', order_index: 9, description: 'Enterprise-grade network equipment', is_default: true },

      // Shades Subcategories
      { name: 'Motorized', system_category: 'shades', order_index: 1, description: 'Motorized and automated shades', is_default: true },
      { name: 'Manual', system_category: 'shades', order_index: 2, description: 'Manual operation window treatments', is_default: true },
      { name: 'Roller', system_category: 'shades', order_index: 3, description: 'Roller shades and blinds', is_default: true },
      { name: 'Roman', system_category: 'shades', order_index: 4, description: 'Roman shades and fabric treatments', is_default: true },
      { name: 'Cellular', system_category: 'shades', order_index: 5, description: 'Cellular and honeycomb shades', is_default: true },
      { name: 'Blackout', system_category: 'shades', order_index: 6, description: 'Light-blocking and blackout shades', is_default: true },
      { name: 'Light Filtering', system_category: 'shades', order_index: 7, description: 'Light filtering window treatments', is_default: true },
      { name: 'Outdoor', system_category: 'shades', order_index: 8, description: 'Exterior shades and awnings', is_default: true },
      { name: 'Smart', system_category: 'shades', order_index: 9, description: 'Smart and connected window treatments', is_default: true },

      // Control Systems Subcategories
      { name: 'Processors', system_category: 'controlSystems', order_index: 1, description: 'Control processors and hubs', is_default: true },
      { name: 'Touch Panels', system_category: 'controlSystems', order_index: 2, description: 'Touch screen control interfaces', is_default: true },
      { name: 'Keypads', system_category: 'controlSystems', order_index: 3, description: 'Control keypads and buttons', is_default: true },
      { name: 'Remotes', system_category: 'controlSystems', order_index: 4, description: 'Remote controls and handheld devices', is_default: true },
      { name: 'Sensors', system_category: 'controlSystems', order_index: 5, description: 'Environmental and occupancy sensors', is_default: true },
      { name: 'Interfaces', system_category: 'controlSystems', order_index: 6, description: 'Control interfaces and modules', is_default: true },
      { name: 'Smart Home', system_category: 'controlSystems', order_index: 7, description: 'Smart home automation systems', is_default: true },
      { name: 'Commercial', system_category: 'controlSystems', order_index: 8, description: 'Commercial control systems', is_default: true },
      { name: 'Integration', system_category: 'controlSystems', order_index: 9, description: 'System integration components', is_default: true }
    ];

    // Insert in batches
    const batchSize = 20;
    for (let i = 0; i < defaultSubcategories.length; i += batchSize) {
      const batch = defaultSubcategories.slice(i, i + batchSize);
      
      const { error: insertError } = await supabase
        .from('product_subcategories')
        .insert(batch);

      if (insertError) {
        console.error('Error inserting default subcategories batch:', insertError);
        return false;
      }
    }

    console.log(`Successfully set up subcategories database with ${defaultSubcategories.length} default subcategories`);
    return true;
  } catch (error) {
    console.error('Error setting up subcategories database:', error);
    return false;
  }
};
