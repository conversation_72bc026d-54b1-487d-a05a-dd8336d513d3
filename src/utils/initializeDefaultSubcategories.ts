import { supabase } from '@/integrations/supabase/client';
import { defaultSubcategories } from '@/data/defaultSubcategories';

export const initializeDefaultSubcategories = async (): Promise<boolean> => {
  try {
    console.log('Initializing default subcategories...');

    // Check if default subcategories already exist
    const { data: existingDefaults, error: checkError } = await supabase
      .from('product_subcategories')
      .select('id')
      .eq('is_default', true)
      .limit(1);

    if (checkError) {
      console.error('Error checking for existing default subcategories:', checkError);
      return false;
    }

    // If default subcategories already exist, skip initialization
    if (existingDefaults && existingDefaults.length > 0) {
      console.log('Default subcategories already exist, skipping initialization');
      return true;
    }

    // Prepare default subcategories for database insertion
    const defaultSubcategoriesForDB = defaultSubcategories.map(subcategory => ({
      id: crypto.randomUUID(),
      name: subcategory.name,
      system_category: subcategory.systemCategory,
      order_index: subcategory.order,
      description: subcategory.description || null,
      is_default: true
    }));

    // Insert default subcategories in batches to avoid potential limits
    const batchSize = 20;
    for (let i = 0; i < defaultSubcategoriesForDB.length; i += batchSize) {
      const batch = defaultSubcategoriesForDB.slice(i, i + batchSize);
      
      const { error: insertError } = await supabase
        .from('product_subcategories')
        .insert(batch);

      if (insertError) {
        console.error('Error inserting default subcategories batch:', insertError);
        return false;
      }
    }

    console.log(`Successfully initialized ${defaultSubcategories.length} default subcategories`);
    return true;
  } catch (error) {
    console.error('Error initializing default subcategories:', error);
    return false;
  }
};

// Function to check if table exists and create it if needed
export const ensureSubcategoriesTableExists = async (): Promise<boolean> => {
  try {
    // Try to query the table to see if it exists
    const { error } = await supabase
      .from('product_subcategories')
      .select('id')
      .limit(1);

    if (error) {
      // If table doesn't exist, we need to create it
      if (error.message.includes('relation "product_subcategories" does not exist')) {
        console.log('Creating product_subcategories table...');
        
        // Create the table using raw SQL
        const createTableSQL = `
          CREATE TABLE IF NOT EXISTS public.product_subcategories (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            name TEXT NOT NULL,
            system_category TEXT NOT NULL,
            order_index INTEGER DEFAULT 0,
            description TEXT,
            is_default BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
          );

          CREATE INDEX IF NOT EXISTS idx_product_subcategories_system_category ON public.product_subcategories(system_category);
          CREATE INDEX IF NOT EXISTS idx_product_subcategories_order ON public.product_subcategories(system_category, order_index);
          CREATE UNIQUE INDEX IF NOT EXISTS idx_product_subcategories_unique_name 
          ON public.product_subcategories(system_category, LOWER(name));

          ALTER TABLE public.product_subcategories ENABLE ROW LEVEL SECURITY;
          
          CREATE POLICY IF NOT EXISTS "Allow all operations for authenticated users" ON public.product_subcategories
          FOR ALL USING (true);
        `;

        // Note: In a real implementation, you would need to execute this SQL
        // For now, we'll assume the table exists or will be created manually
        console.log('Table creation SQL prepared (manual execution required)');
        return true;
      } else {
        console.error('Error checking subcategories table:', error);
        return false;
      }
    }

    console.log('product_subcategories table exists');
    return true;
  } catch (error) {
    console.error('Error ensuring subcategories table exists:', error);
    return false;
  }
};

// Combined initialization function
export const initializeSubcategorySystem = async (): Promise<boolean> => {
  try {
    // First ensure the table exists
    const tableExists = await ensureSubcategoriesTableExists();
    if (!tableExists) {
      console.error('Failed to ensure subcategories table exists');
      return false;
    }

    // Then initialize default subcategories
    const defaultsInitialized = await initializeDefaultSubcategories();
    if (!defaultsInitialized) {
      console.error('Failed to initialize default subcategories');
      return false;
    }

    console.log('Subcategory system initialization completed successfully');
    return true;
  } catch (error) {
    console.error('Error during subcategory system initialization:', error);
    return false;
  }
};
