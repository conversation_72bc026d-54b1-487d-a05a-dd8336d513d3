import jsPDF from 'jspdf';
import { FormData } from '@/types/formTypes';
import autoTable from 'jspdf-autotable';
import { format } from 'date-fns';
import { getWeQuotePackages } from '@/services/zapierService';
import { CostSummary } from '@/models/CostStructure';
import { generateCostSummary, calculateCategoryTotal } from '@/services/costService';

// Add a debounce flag to prevent multiple generations
let isGenerating = false;

export const generateProjectPDF = (formData: FormData) => {
  // If already generating, exit early
  if (isGenerating) {
    console.log('PDF generation already in progress - preventing duplicate generation');
    return;
  }
  
  isGenerating = true;
  
  try {
    const { clientInfo, qualificationInfo, rooms, systemCategories, executiveSummary } = formData;
    const costEstimate = (formData as any)?.costEstimate;
    const allPackages = getWeQuotePackages();
    
    // Create a new document
    const doc = new jsPDF({
      orientation: 'portrait',
      unit: 'mm',
      format: 'a4',
    });

    // Set up variables for positioning
    let currentY = 20;
    const pageWidth = doc.internal.pageSize.width;
    const margin = 20;
    const contentWidth = pageWidth - (margin * 2);

    // Add header
    doc.setFont("helvetica", "bold");
    doc.setFontSize(22);
    doc.text("Client Project Walkthrough", pageWidth / 2, currentY, { align: "center" });
    currentY += 10;

    // Add logo (if available)
    const logo = new Image();
    logo.src = '/lovable-uploads/b236b3b0-3175-4672-8fad-ee4e29a85315.png';
    
    // Use a Promise to handle logo loading
    const loadLogoPromise = new Promise<void>((resolve) => {
      logo.onload = () => {
        // Add logo to the top right corner
        const logoWidth = 30;
        const logoHeight = logoWidth * (logo.height / logo.width);
        doc.addImage(logo, 'PNG', pageWidth - margin - logoWidth, margin - 10, logoWidth, logoHeight);
        resolve();
      };
      
      // Handle loading failures with a timeout
      const timeout = setTimeout(() => {
        console.warn("Logo image loading timed out, generating PDF without logo");
        resolve();
      }, 2000);
      
      logo.onerror = () => {
        console.warn("Error loading logo, generating PDF without logo");
        clearTimeout(timeout);
        resolve();
      };
    });

    // Wait for the logo to load (or timeout) then continue with PDF generation
    loadLogoPromise.then(() => {
      // Add date
      doc.setFont("helvetica", "normal");
      doc.setFontSize(10);
      doc.text(`Generated on: ${format(new Date(), 'MMMM d, yyyy')}`, pageWidth / 2, currentY, { align: "center" });
      currentY += 15;

      // Client Information Section
      doc.setFont("helvetica", "bold");
      doc.setFontSize(16);
      doc.text("Client Information", margin, currentY);
      doc.setLineWidth(0.5);
      doc.line(margin, currentY + 2, pageWidth - margin, currentY + 2);
      currentY += 10;

      doc.setFont("helvetica", "normal");
      doc.setFontSize(12);
      
      const clientInfoText = [
        `Name: ${clientInfo.fullName || 'Not specified'}`,
        `Email: ${clientInfo.email || 'Not specified'}`,
        `Phone: ${clientInfo.phone || 'Not specified'}`,
        `Address: ${clientInfo.address || 'Not specified'}`,
        `Project Type: ${clientInfo.projectType || 'Not specified'}`,
      ];

      clientInfoText.forEach(text => {
        doc.text(text, margin, currentY);
        currentY += 7;
      });
      currentY += 5;

      // Project Qualification Section
      doc.setFont("helvetica", "bold");
      doc.setFontSize(16);
      doc.text("Project Qualification", margin, currentY);
      doc.setLineWidth(0.5);
      doc.line(margin, currentY + 2, pageWidth - margin, currentY + 2);
      currentY += 10;

      doc.setFont("helvetica", "normal");
      doc.setFontSize(12);
      
      const qualificationText = [
        `Square Footage: ${qualificationInfo.squareFootage?.toLocaleString() || 'Not specified'} sq ft`,
        `Budget Range: $${qualificationInfo.budget ? qualificationInfo.budget.toLocaleString() : 'Not specified'}`,
        `Timeline: ${qualificationInfo.projectStage || 'Not specified'}`,
        `Construction Type: ${qualificationInfo.projectStage || 'Not specified'}`
      ];

      qualificationText.forEach(text => {
        doc.text(text, margin, currentY);
        currentY += 7;
      });
      currentY += 5;

      // Selected Systems Section
      doc.setFont("helvetica", "bold");
      doc.setFontSize(16);
      doc.text("Selected Systems", margin, currentY);
      doc.setLineWidth(0.5);
      doc.line(margin, currentY + 2, pageWidth - margin, currentY + 2);
      currentY += 10;

      doc.setFont("helvetica", "normal");
      doc.setFontSize(12);
      
      const systemsArray = Object.entries(systemCategories)
        .filter(([_, isSelected]) => isSelected)
        .map(([system, _]) => {
          // Convert systemName to title case for better readability
          return system.charAt(0).toUpperCase() + system.slice(1);
        });

      if (systemsArray.length > 0) {
        doc.text(`• ${systemsArray.join('\n• ')}`, margin, currentY);
        currentY += (systemsArray.length * 7) + 5;
      } else {
        doc.text("No systems selected", margin, currentY);
        currentY += 7 + 5;
      }

      // Executive Summary Section if available
      if (executiveSummary) {
        // Check if we need a new page
        if (currentY > 220) {
          doc.addPage();
          currentY = 20;
        }

        doc.setFont("helvetica", "bold");
        doc.setFontSize(16);
        doc.text("Executive Summary", margin, currentY);
        doc.setLineWidth(0.5);
        doc.line(margin, currentY + 2, pageWidth - margin, currentY + 2);
        currentY += 10;

        doc.setFont("helvetica", "normal");
        doc.setFontSize(11);
        
        const summaryLines = doc.splitTextToSize(executiveSummary, contentWidth);
        doc.text(summaryLines, margin, currentY);
        currentY += (summaryLines.length * 5) + 10;
      }

      // Format as currency function
      const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-US', {
          style: 'currency',
          currency: 'USD',
          maximumFractionDigits: 0
        }).format(amount);
      };

      // Function to calculate package cost
      const calculatePackageCost = (packageId: string | undefined): number => {
        if (!packageId) return 0;
        const pkg = allPackages.find(p => p.id === packageId);
        if (!pkg || !pkg.items || pkg.items.length === 0) return 0;
        
        return pkg.items.reduce((total, item) => {
          const itemCost = (item.unitCost || 0) * (item.quantity || 1);
          return total + itemCost;
        }, 0);
      };
      
      // Function to get package name by ID
      const getPackageName = (packageId: string | undefined, systemType: string) => {
        if (!packageId) return "Default Package";
        const pkg = allPackages.find(p => p.id === packageId);
        return pkg?.name || "Custom Package";
      };

      // Room Details Section with Pricing
      if (rooms.length > 0) {
        // Check if we need a new page
        if (currentY > 200) {
          doc.addPage();
          currentY = 20;
        }

        doc.setFont("helvetica", "bold");
        doc.setFontSize(16);
        doc.text("Room Details & Package Selections", margin, currentY);
        doc.setLineWidth(0.5);
        doc.line(margin, currentY + 2, pageWidth - margin, currentY + 2);
        currentY += 10;

        // Track total cost for each room
        const roomCosts: {roomName: string, cost: number}[] = [];

        rooms.forEach(room => {
          // Check if we need a new page
          if (currentY > 250) {
            doc.addPage();
            currentY = 20;
          }

          doc.setFont("helvetica", "bold");
          doc.setFontSize(14);
          doc.text(room.name, margin, currentY);
          currentY += 7;

          doc.setFont("helvetica", "normal");
          doc.setFontSize(12);

          // Calculate room cost and create a table of systems, packages, and costs for this room
          let roomTotalCost = 0;
          const roomSystemsData = [];
          
          // Check for lighting
          if (room.lighting) {
            const packageCost = calculatePackageCost(room.lightingPackageId);
            roomSystemsData.push([
              "Lighting", 
              getPackageName(room.lightingPackageId, "lighting"),
              formatCurrency(packageCost)
            ]);
            roomTotalCost += packageCost;
          }
          
          // Check for shades
          if (room.shades !== "none") {
            const packageCost = calculatePackageCost(room.shadesPackageId);
            roomSystemsData.push([
              "Shades", 
              getPackageName(room.shadesPackageId, "shades"),
              formatCurrency(packageCost)
            ]);
            roomTotalCost += packageCost;
          }
          
          // Check for audio
          if (room.audio !== "none") {
            const packageCost = calculatePackageCost(room.audioPackageId);
            roomSystemsData.push([
              "Audio", 
              getPackageName(room.audioPackageId, "audio"),
              formatCurrency(packageCost)
            ]);
            roomTotalCost += packageCost;
          }
          
          // Check for video
          if (room.video !== "none") {
            const packageCost = calculatePackageCost(room.videoPackageId);
            roomSystemsData.push([
              "Video", 
              getPackageName(room.videoPackageId, "video"),
              formatCurrency(packageCost)
            ]);
            roomTotalCost += packageCost;
          }
          
          // Check for network
          if (room.network === "high density") {
            const packageCost = calculatePackageCost(room.networkPackageId);
            roomSystemsData.push([
              "Network", 
              getPackageName(room.networkPackageId, "network"),
              formatCurrency(packageCost)
            ]);
            roomTotalCost += packageCost;
          }
          
          // Check for security
          if (room.surveillance) {
            const packageCost = calculatePackageCost(room.securityPackageId);
            roomSystemsData.push([
              "Security", 
              getPackageName(room.securityPackageId, "security"),
              formatCurrency(packageCost)
            ]);
            roomTotalCost += packageCost;
          }
          
          // Check for control systems
          if (room.control !== "none") {
            const packageCost = calculatePackageCost(room.controlSystemsPackageId);
            roomSystemsData.push([
              "Control Systems", 
              getPackageName(room.controlSystemsPackageId, "controlSystems"),
              formatCurrency(packageCost)
            ]);
            roomTotalCost += packageCost;
          }

          // Store the room cost for the summary
          roomCosts.push({
            roomName: room.name,
            cost: roomTotalCost
          });

          if (roomSystemsData.length > 0) {
            // Add room total row
            roomSystemsData.push([
              "ROOM TOTAL", 
              "", 
              formatCurrency(roomTotalCost)
            ]);

            autoTable(doc, {
              startY: currentY,
              head: [['System Type', 'Package Selected', 'Cost']],
              body: roomSystemsData,
              margin: { left: margin, right: margin },
              headStyles: { fillColor: [55, 55, 47] },
              styles: { fontSize: 10 },
              theme: 'grid',
              tableWidth: contentWidth,
              foot: [['ROOM TOTAL', '', formatCurrency(roomTotalCost)]],
              footStyles: { fontStyle: 'bold', fillColor: [220, 220, 220] }
            });
            
            // Update currentY after the table
            const finalY = (doc as any).lastAutoTable.finalY;
            currentY = finalY + 10;
          } else {
            doc.text("No systems selected for this room", margin, currentY);
            currentY += 7;
          }

          // Add notes if available
          if (room.notes) {
            doc.setFont("helvetica", "italic");
            doc.setFontSize(10);
            doc.text("Notes:", margin, currentY);
            currentY += 5;
            
            // Wrap text to fit within margins
            const textLines = doc.splitTextToSize(room.notes, contentWidth - 10);
            doc.text(textLines, margin + 5, currentY);
            currentY += (textLines.length * 5) + 5;
          }

          currentY += 5; // Add a bit more space after each room
        });

        // Add a total project cost summary
        if (roomCosts.length > 0) {
          // Always add this on a new page
          doc.addPage();
          currentY = 20;
          
          doc.setFont("helvetica", "bold");
          doc.setFontSize(16);
          doc.text("Cost Summary by Room", margin, currentY);
          doc.setLineWidth(0.5);
          doc.line(margin, currentY + 2, pageWidth - margin, currentY + 2);
          currentY += 10;
          
          // Create a summary table of room costs
          const roomCostData = roomCosts.map(room => [room.roomName, formatCurrency(room.cost)]);
          const projectTotal = roomCosts.reduce((total, room) => total + room.cost, 0);
          
          autoTable(doc, {
            startY: currentY,
            head: [['Room', 'Cost']],
            body: roomCostData,
            margin: { left: margin, right: margin },
            headStyles: { fillColor: [55, 55, 47] },
            styles: { fontSize: 10 },
            theme: 'grid',
            tableWidth: contentWidth,
            foot: [['PROJECT TOTAL', formatCurrency(projectTotal)]],
            footStyles: { fontStyle: 'bold', fillColor: [220, 220, 220] }
          });
          
          // Update currentY after the table
          const finalY = (doc as any).lastAutoTable.finalY;
          currentY = finalY + 15;
          
          // Add tax and total calculation
          const taxRate = 8.25; // Using default tax rate, could be made configurable
          const taxAmount = projectTotal * (taxRate / 100);
          const grandTotal = projectTotal + taxAmount;
          
          doc.setFont("helvetica", "normal");
          doc.setFontSize(12);
          doc.text(`Subtotal: ${formatCurrency(projectTotal)}`, margin, currentY);
          currentY += 7;
          doc.text(`Tax (${taxRate}%): ${formatCurrency(taxAmount)}`, margin, currentY);
          currentY += 7;
          doc.setFont("helvetica", "bold");
          doc.text(`Grand Total: ${formatCurrency(grandTotal)}`, margin, currentY);
          currentY += 15;
          
          doc.setFont("helvetica", "italic");
          doc.setFontSize(10);
          doc.text("Note: This is an estimate based on selected packages and may change based on final design and installation requirements.", 
            margin, currentY, { maxWidth: contentWidth });
        }
      }

      // System Packages Summary Section
      if (Object.values(systemCategories).some(Boolean)) {
        // Check if we need a new page
        if (currentY > 200) {
          doc.addPage();
          currentY = 20;
        }

        doc.setFont("helvetica", "bold");
        doc.setFontSize(16);
        doc.text("System Package Details", margin, currentY);
        doc.setLineWidth(0.5);
        doc.line(margin, currentY + 2, pageWidth - margin, currentY + 2);
        currentY += 10;

        // Get unique packages used across all rooms
        const uniquePackages = new Set();
        rooms.forEach(room => {
          if (room.lightingPackageId) uniquePackages.add(room.lightingPackageId);
          if (room.shadesPackageId) uniquePackages.add(room.shadesPackageId);
          if (room.audioPackageId) uniquePackages.add(room.audioPackageId);
          if (room.videoPackageId) uniquePackages.add(room.videoPackageId);
          if (room.networkPackageId) uniquePackages.add(room.networkPackageId);
          if (room.securityPackageId) uniquePackages.add(room.securityPackageId);
          if (room.controlSystemsPackageId) uniquePackages.add(room.controlSystemsPackageId);
        });

        // Filter packages to only include those in use
        const usedPackages = allPackages.filter(pkg => uniquePackages.has(pkg.id));

        if (usedPackages.length > 0) {
          usedPackages.forEach(pkg => {
            // Check if we need a new page
            if (currentY > 240) {
              doc.addPage();
              currentY = 20;
            }

            doc.setFont("helvetica", "bold");
            doc.setFontSize(12);
            doc.text(`${pkg.name} (${pkg.systemCategory.charAt(0).toUpperCase() + pkg.systemCategory.slice(1)})`, margin, currentY);
            currentY += 5;

            doc.setFont("helvetica", "normal");
            doc.setFontSize(10);
            if (pkg.description) {
              const descLines = doc.splitTextToSize(`Description: ${pkg.description}`, contentWidth - 10);
              doc.text(descLines, margin + 5, currentY);
              currentY += (descLines.length * 4) + 2;
            }

            if (pkg.items && pkg.items.length > 0) {
              // Table for package items
              const itemsData = pkg.items.map(item => [
                item.name || 'Unnamed Item',
                item.quantity.toString(),
                item.description || ''
              ]);

              autoTable(doc, {
                startY: currentY,
                head: [['Component', 'Qty', 'Description']],
                body: itemsData,
                margin: { left: margin + 5, right: margin },
                headStyles: { fillColor: [75, 75, 75] },
                styles: { fontSize: 9 },
                theme: 'striped',
                tableWidth: contentWidth - 10
              });
              
              // Update currentY after the table
              const finalY = (doc as any).lastAutoTable.finalY;
              currentY = finalY + 8;
            } else {
              doc.text("No items specified for this package", margin + 5, currentY);
              currentY += 5;
            }
          });
        } else {
          doc.text("No package details available", margin, currentY);
          currentY += 7;
        }
      }

      // Scope of Work Section (if available)
      if (qualificationInfo.generatedScopeOfWork) {
        // Always start the scope of work on a new page
        doc.addPage();
        currentY = 20;

        doc.setFont("helvetica", "bold");
        doc.setFontSize(16);
        doc.text("Scope of Work", margin, currentY);
        doc.setLineWidth(0.5);
        doc.line(margin, currentY + 2, pageWidth - margin, currentY + 2);
        currentY += 10;

        doc.setFont("helvetica", "normal");
        doc.setFontSize(11);
        
        const scopeLines = doc.splitTextToSize(qualificationInfo.generatedScopeOfWork, contentWidth);
        doc.text(scopeLines, margin, currentY);
        currentY += (scopeLines.length * 5);
      }

      // Cost Estimate Section (if available)
      if (costEstimate) {
        // Check if we need a new page
        if (currentY > 200) {
          doc.addPage();
          currentY = 20;
        }

        doc.setFont("helvetica", "bold");
        doc.setFontSize(16);
        doc.text("Cost Estimate", margin, currentY);
        doc.setLineWidth(0.5);
        doc.line(margin, currentY + 2, pageWidth - margin, currentY + 2);
        currentY += 10;

        doc.setFont("helvetica", "normal");
        doc.setFontSize(12);

        // Format as currency
        const formatCurrency = (amount: number) => {
          return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD',
            maximumFractionDigits: 0
          }).format(amount);
        };

        doc.text(`Total Estimate: ${formatCurrency(costEstimate.totalCost)}`, margin, currentY);
        currentY += 7;
        doc.text(`Generated on: ${format(new Date(costEstimate.generated), 'MMMM d, yyyy')}`, margin, currentY);
        currentY += 12;

        // Breakdown table
        if (Object.keys(costEstimate.breakdown).length > 0) {
          const tableData = Object.entries(costEstimate.breakdown).map(([system, amount]) => {
            // Convert system name for display
            const displayName = system.charAt(0).toUpperCase() + system.slice(1).replace(/([A-Z])/g, ' $1');
            return [displayName, formatCurrency(amount as number)];
          });

          autoTable(doc, {
            startY: currentY,
            head: [['System', 'Amount']],
            body: tableData,
            margin: { left: margin, right: margin },
            headStyles: { fillColor: [55, 55, 47] },
          });
          
          // Update currentY after the table
          const finalY = (doc as any).lastAutoTable.finalY;
          currentY = finalY + 10;
        }
      }

      // Footer
      const totalPages = doc.getNumberOfPages();
      for (let i = 1; i <= totalPages; i++) {
        doc.setPage(i);
        doc.setFont("helvetica", "italic");
        doc.setFontSize(10);
        doc.text(
          `Page ${i} of ${totalPages} | Innovative Client Discovery | ${format(new Date(), 'yyyy')}`, 
          pageWidth / 2, 
          doc.internal.pageSize.height - 10, 
          { align: "center" }
        );
      }

      // Save the PDF
      doc.save(`${clientInfo.fullName || 'Client'}_Project.pdf`);
      
      // Reset the generating flag
      isGenerating = false;
    });
    
  } catch (error) {
    console.error('Error generating PDF:', error);
    // Reset the generating flag in case of error
    isGenerating = false;
    throw error;
  }
};
